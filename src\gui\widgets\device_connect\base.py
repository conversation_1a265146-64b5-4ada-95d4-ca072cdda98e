from PySide6.QtWidgets import <PERSON><PERSON>rame, QLabel, QHBoxLayout
from src.utils.resource_helper import get_image


# 协议连接面板基类
class ProtocolWidgetBase(QFrame):
    def __init__(self, parent=None):
        super().__init__(parent)


        self.setStyleSheet("background-color: #2B2E35; border: none; border-radius: 5px;")

    def get_config(self):
        raise NotImplementedError


# 协议参数界面基类
class ParamWidgetBase(QFrame):
    def __init__(self, name, parent=None):
        super().__init__(parent)
        self.setStyleSheet("background-color: #30393A; border: 1px; border-radius: 5px;")
        self.setFixedSize(278, 80)

        # icon
        self.icon_label = QLabel()
        self.icon_label.setFixedSize(22, 22)
        self.icon_label.setPixmap(get_image("settings.svg"))

        # name
        self.name_label = QLabel("   " + name + "   ")
        self.name_label.setFixedWidth(120)
        self.name_label.setStyleSheet("""
            color: #FFFFFF;
            background-color: transparent;
            font-family: 'Alibaba PuHuiTi 2.0';
            font-size: 20px;
            font-weight: 300;
            letter-spacing: 0px;  /* 可能不支持，依赖平台 */
            border: none;
        """)

        self.setLayout(QHBoxLayout())
        self.layout().setSpacing(0)
        self.layout().setContentsMargins(25, 15, 10, 15)
        self.layout().addWidget(self.icon_label)
        self.layout().addWidget(self.name_label)

    def insert_input_widget(self, widget):
        self.layout().addWidget(widget)
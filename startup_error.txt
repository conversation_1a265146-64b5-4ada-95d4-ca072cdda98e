启动错误: while parsing a flow mapping
  in "E:\BrainCo_Revo_hand_tool\settings.yaml", line 8, column 11
expected ',' or '}', but got '<stream end>'
  in "E:\BrainCo_Revo_hand_tool\settings.yaml", line 15, column 1
Traceback (most recent call last):
  File "E:\BrainCo_Revo_hand_tool\main.py", line 90, in <module>
    register_parameters_to_global(os.path.join(APP_ROOT_PATH, "settings.yaml"))
  File "E:\BrainCo_Revo_hand_tool\src\utils\config_loader.py", line 24, in register_parameters_to_global
    config = yaml.load(f)
  File "D:\Envs_39\brainco_revo_tool\lib\site-packages\ruamel\yaml\main.py", line 453, in load
    return constructor.get_single_data()
  File "D:\Envs_39\brainco_revo_tool\lib\site-packages\ruamel\yaml\constructor.py", line 117, in get_single_data
    node = self.composer.get_single_node()
  File "D:\Envs_39\brainco_revo_tool\lib\site-packages\ruamel\yaml\composer.py", line 72, in get_single_node
    document = self.compose_document()
  File "D:\Envs_39\brainco_revo_tool\lib\site-packages\ruamel\yaml\composer.py", line 95, in compose_document
    node = self.compose_node(None, None)
  File "D:\Envs_39\brainco_revo_tool\lib\site-packages\ruamel\yaml\composer.py", line 130, in compose_node
    node = self.compose_mapping_node(anchor)
  File "D:\Envs_39\brainco_revo_tool\lib\site-packages\ruamel\yaml\composer.py", line 212, in compose_mapping_node
    item_value = self.compose_node(node, item_key)
  File "D:\Envs_39\brainco_revo_tool\lib\site-packages\ruamel\yaml\composer.py", line 130, in compose_node
    node = self.compose_mapping_node(anchor)
  File "D:\Envs_39\brainco_revo_tool\lib\site-packages\ruamel\yaml\composer.py", line 205, in compose_mapping_node
    while not self.parser.check_event(MappingEndEvent):
  File "D:\Envs_39\brainco_revo_tool\lib\site-packages\ruamel\yaml\parser.py", line 141, in check_event
    self.current_event = self.state()
  File "D:\Envs_39\brainco_revo_tool\lib\site-packages\ruamel\yaml\parser.py", line 747, in parse_flow_mapping_key
    raise ParserError(
ruamel.yaml.parser.ParserError: while parsing a flow mapping
  in "E:\BrainCo_Revo_hand_tool\settings.yaml", line 8, column 11
expected ',' or '}', but got '<stream end>'
  in "E:\BrainCo_Revo_hand_tool\settings.yaml", line 15, column 1

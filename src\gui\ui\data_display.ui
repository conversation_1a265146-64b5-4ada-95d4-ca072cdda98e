<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>863</width>
    <height>372</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout" stretch="0,0,1">
   <property name="spacing">
    <number>7</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <property name="spacing">
      <number>16</number>
     </property>
     <item>
      <widget class="QPushButton" name="pushButton_chart">
       <property name="text">
        <string>图表展示</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_number">
       <property name="text">
        <string>数字显示</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_3">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="CustomRadioButton" name="radioButton_number">
       <property name="text">
        <string>Number</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="CustomRadioButton" name="radioButton_percentage">
       <property name="text">
        <string>Percentage</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <property name="spacing">
      <number>16</number>
     </property>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeType">
        <enum>QSizePolicy::Fixed</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_position">
       <property name="text">
        <string>Position</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_speed">
       <property name="text">
        <string>Speed</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_force">
       <property name="text">
        <string>Force</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_4">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QTabWidget" name="tabWidget"/>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>CustomRadioButton</class>
   <extends>QRadioButton</extends>
   <header>..components.radiobutton</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>

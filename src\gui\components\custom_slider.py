from PySide6.QtCore import Signal, Qt, QRect
from PySide6.QtGui import Q<PERSON>ainter, QPainterPath, QBrush, QColor, QPen
from PySide6.QtWidgets import QWidget, QFrame, QHBoxLayout, QSlider, QVBoxLayout, QLabel, QPushButton, QSizePolicy



class CustomHandleSlider(QSlider):
    value_changed = Signal(int)

    def __init__(self, orientation, parent=None):
        super().__init__(orientation, parent)
        self.setStyleSheet("""
            QSlider {
                background: transparent;
            }
            
            QSlider:horizontal {
                min-height: 30px;
                margin: 0;
                padding: 0;
                border: 0;
            }

            QSlider::groove:vertical {
                width: 1px;
                background: white;
                margin: 0;
            }

            QSlider::groove:horizontal {
                height: 1px;
                background: white;
                margin: 0;
            }

            /* Make default handle invisible but keep its size and functionality */
            QSlider::handle:vertical {
                background: transparent;
                width: 30px;
                height: 30px;
                margin-left: -15px;
                margin-right: -15px;
            }

            QSlider::handle:horizontal {
                background: transparent;
                width: 30px;
                height: 30px;
                margin-left: 0px;
                margin-top: -15px;
                margin-bottom: -15px;
            }
        """)

        # Make the slider more responsive
        self.setTracking(True)
        self.setMouseTracking(True)

        self.setContentsMargins(0, 0, 0, 0)

        self.wheelEvent = None

    def paintEvent(self, event):
        # First draw the basic slider
        super().paintEvent(event)

        # Then draw our custom handle
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Get handle position
        handle_pos = self._get_handle_position()

        # Draw square handle
        handle_size = 25
        if self.orientation() == Qt.Vertical:
            handle_rect = QRect(
                (self.width() - handle_size) // 2,
                handle_pos - handle_size // 2,
                handle_size,
                handle_size
            )
        else:
            handle_rect = QRect(
                handle_pos - handle_size // 2,
                (self.height() - handle_size) // 2,
                handle_size,
                handle_size
            )

        # Draw gray square
        painter.fillRect(handle_rect, QColor(80, 80, 80))

        # Draw white circle in center
        circle_size = 15
        circle_rect = QRect(
            handle_rect.center().x() - circle_size // 2,
            handle_rect.center().y() - circle_size // 2,
            circle_size,
            circle_size
        )

        if self.isEnabled():
            painter.setPen(QPen(Qt.white, 2))
        else:
            painter.setPen(QPen(Qt.gray, 2))
        painter.setBrush(Qt.NoBrush)
        painter.drawEllipse(circle_rect)

    def _get_handle_position(self):
        """Calculate the position of the slider handle"""
        span = self.maximum() - self.minimum()
        if span == 0:
            return 0

        ratio = (self.value() - self.minimum()) / span

        if self.orientation() == Qt.Vertical:
            available_space = self.height() - 30  # Subtract handle height
            if self.invertedAppearance():
                return int(ratio * available_space + 15)
            else:
                return int((1 - ratio) * available_space + 15)
        else:
            available_space = self.width() - 30  # Subtract handle width
            return int(ratio * available_space + 15)

    def mouseReleaseEvent(self, ev, /):
        super().mouseReleaseEvent(ev)
        self.value_changed.emit(self.value())


class FingerSliderBase(QFrame):
    value_changed = Signal(int)

    def __init__(self, parent=None):
        super().__init__(parent)
        self._pressing = False
        self._init_ui()
        self.setup_ui()

    def _init_ui(self):
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(2)

    def setup_ui(self):
        pass


class FingerSliderV(FingerSliderBase):

    def __init__(self, parent=None):
        super().__init__(parent)

    def setup_ui(self):

        # 上下按钮和滑动条布局
        slider_layout = QVBoxLayout()
        slider_layout.setContentsMargins(2, 2, 2, 2)
        slider_layout.setSpacing(0)

        # 滑块容器
        self.slider_container = CustomHandleSlider(Qt.Orientation.Vertical, self)
        self.slider_container.setMinimumWidth(40)
        self.slider_container.setInvertedAppearance(True)

        # 组装滑动条布局
        slider_layout.addWidget(self.slider_container)

        # 添加到主布局
        self.main_layout.addLayout(slider_layout)
        self.main_layout.setAlignment(Qt.AlignCenter)


class FingerSliderH(FingerSliderBase):

    def __init__(self, parent=None):
        super().__init__(parent)

    def setup_ui(self):

        # 上下按钮和滑动条布局
        slider_layout = QHBoxLayout()
        slider_layout.setContentsMargins(2, 2, 2, 2)
        slider_layout.setSpacing(0)

        # 滑块容器
        self.slider_container = CustomHandleSlider(Qt.Orientation.Horizontal, self)
        self.slider_container.setFixedHeight(40)

        # 添加到主布局
        self.main_layout.addWidget(self.slider_container)
        self.main_layout.setAlignment(Qt.AlignCenter)
# src/data/hand_specifications.py

from enum import IntEnum, Enum
from dataclasses import dataclass
from typing import Dict, List, Optional, Union, Any

from src.core import HandVersion, ProtocolType, Baudrate


class HandType(IntEnum):
    Left = 1
    Right = 2


@dataclass
class ModbusParameters:
    timeout: float  # 超时时间(秒)
    retry_count: int  # 重试次数
    slave_address: int  # 从机地址
    supported_baudrates: tuple
    default_baudrate: int  # 波特率
    slave_address_range: tuple


class HandSpecification:
    def __init__(self, version, default_protocol, supported_protocols: list, protocol_parameters: dict):
        self.version = version
        self.supported_protocols = supported_protocols
        self.default_protocol = default_protocol
        self.protocol_parameters = protocol_parameters



HAND_SPECIFICATIONS = {
    HandType.Left: HandSpecification(
        version=HandVersion.V2,
        supported_protocols=[ProtocolType.Modbus],
        default_protocol=ProtocolType.Modbus,
        protocol_parameters = {
            ProtocolType.Modbus: ModbusParameters(timeout=1.0,
                                                  retry_count=3,
                                                  slave_address=126,
                                                  default_baudrate=460800,
                                                  supported_baudrates=(19200, 57600, 115200, 460800, 1000000, 2000000, 5000000),
                                                  slave_address_range=(1, 254))
        }
    )
}


INTERFACE_CONNECTION_PARAMETERS = {
    "supported_protocols": [ProtocolType.Modbus],
    "default_protocol": ProtocolType.Modbus,
    "protocol_parameters": {
            ProtocolType.Modbus: ModbusParameters(timeout=1.0,
                                                  retry_count=3,
                                                  slave_address=126,
                                                  default_baudrate=460800,
                                                  supported_baudrates=(19200, 57600, 115200, 460800, 1000000, 2000000, 5000000),
                                                  slave_address_range=(1, 254))
        }
}


# -*- coding: utf-8 -*-
"""
运动学计算模块

提供机器人运动学正向计算功能
"""

import numpy as np
from typing import Dict, List


class SimpleTransformCalculator:

    def __init__(self, robot):
        """
        初始化简化的变换计算

        Args:
            robot: URDF机器人模型对象
        """
        self.robot = robot
        self.joint_values = {}      # 存储所有关节的当前值
        self.link_transforms = {}   # 存储所有链接的变换矩阵

        # 初始化所有非固定关节的值为0
        for joint in self.robot.joints:
            if joint.type != 'fixed':
                self.joint_values[joint.name] = 0.0

        # 计算初始变换矩阵
        self.calculate_all_transforms()

    def update_joint_value(self, joint_name: str, value: float):
        """
        更新关节值并重新计算变换

        当关节值发生变化时，需要重新计算所有受影响链接的变换矩阵

        Args:
            joint_name (str): 关节名称
            value (float): 新的关节值（角度或位移）

        Returns:
            List[str]: 受影响的链接名称列表
        """
        if joint_name in self.joint_values:
            self.joint_values[joint_name] = value
            # 重新计算所有变换（简单但可靠的方法）
            self.calculate_all_transforms()
            return self.get_affected_links(joint_name)
        return []

    def get_affected_links(self, joint_name: str):
        """
        获取受关节影响的链接

        当一个关节运动时，该关节的子链接及其所有后代链接都会受到影响

        Args:
            joint_name (str): 关节名称

        Returns:
            List[str]: 受影响的链接名称列表
        """
        affected = []
        joint = next((j for j in self.robot.joints if j.name == joint_name), None)
        if joint:
            affected.append(joint.child)
            # 递归查找所有子链接
            self._find_child_links(joint.child, affected)
        return affected

    def _find_child_links(self, parent_link: str, affected: list):
        """
        递归查找子链接

        在运动学树中，一个链接的运动会影响其所有子链接

        Args:
            parent_link (str): 父链接名称
            affected (list): 受影响链接列表（会被修改）
        """
        child_joints = [j for j in self.robot.joints if j.parent == parent_link]
        for child_joint in child_joints:
            affected.append(child_joint.child)
            self._find_child_links(child_joint.child, affected)

    def calculate_all_transforms(self):
        """
        计算所有链接的变换矩阵

        使用递归方法按照运动学树的依赖关系计算每个链接的变换矩阵。
        每个链接的变换是其父链接变换与连接关节变换的组合。
        """
        self.link_transforms = {}
        processed = set()  # 记录已处理的链接，避免重复计算

        def calculate_link_transform(link_name):
            """
            递归计算单个链接的变换矩阵

            Args:
                link_name (str): 链接名称

            Returns:
                np.ndarray: 4x4变换矩阵
            """
            if link_name in processed:
                return self.link_transforms.get(link_name, np.eye(4))

            # 查找连接到此链接的关节
            joint = next((j for j in self.robot.joints if j.child == link_name), None)
            if not joint:
                # 这是根链接（base_link），使用单位矩阵
                transform = np.eye(4)
                self.link_transforms[link_name] = transform
                processed.add(link_name)
                return transform

            # 确保父链接的变换已经计算
            parent_transform = calculate_link_transform(joint.parent)

            # 计算当前关节的变换
            joint_transform = self.calculate_joint_transform(joint)

            # 组合父链接变换和关节变换
            link_transform = np.dot(parent_transform, joint_transform)
            self.link_transforms[link_name] = link_transform
            processed.add(link_name)
            return link_transform

        # 处理所有链接
        for link in self.robot.links:
            calculate_link_transform(link.name)

    def calculate_joint_transform(self, joint):
        """
        计算单个关节的变换矩阵

        关节变换由两部分组成：
        1. 关节原点变换（origin transform）：关节在父链接坐标系中的位置和姿态
        2. 关节运动变换（motion transform）：由关节值产生的运动变换

        Args:
            joint: URDF关节对象

        Returns:
            np.ndarray: 4x4变换矩阵
        """
        # 1. 计算关节原点变换
        if joint.origin is not None:
            xyz = np.array(joint.origin.xyz)  # 位置偏移
            rpy = np.array(joint.origin.rpy)  # 姿态（Roll-Pitch-Yaw）

            # 将RPY角度转换为旋转矩阵
            roll, pitch, yaw = rpy
            c_r, s_r = np.cos(roll), np.sin(roll)
            c_p, s_p = np.cos(pitch), np.sin(pitch)
            c_y, s_y = np.cos(yaw), np.sin(yaw)

            # 分别计算绕X、Y、Z轴的旋转矩阵
            Rx = np.array([[1, 0, 0], [0, c_r, -s_r], [0, s_r, c_r]])
            Ry = np.array([[c_p, 0, s_p], [0, 1, 0], [-s_p, 0, c_p]])
            Rz = np.array([[c_y, -s_y, 0], [s_y, c_y, 0], [0, 0, 1]])

            # 组合旋转矩阵（ZYX顺序）
            origin_transform = np.eye(4)
            origin_transform[:3, :3] = np.dot(np.dot(Rz, Ry), Rx)
            origin_transform[:3, 3] = xyz
        else:
            origin_transform = np.eye(4)

        # 2. 计算关节运动变换
        joint_value = self.joint_values.get(joint.name, 0.0)

        if joint.type == 'revolute':
            # 旋转关节：绕指定轴旋转
            axis = np.array(joint.axis)
            if np.linalg.norm(axis) > 0:
                axis = axis / np.linalg.norm(axis)  # 归一化轴向量

                # 使用Rodrigues公式计算绕任意轴的旋转矩阵
                c = np.cos(joint_value)
                s = np.sin(joint_value)
                t = 1 - c
                x, y, z = axis

                joint_rotation = np.array([
                    [t*x*x + c,    t*x*y - s*z,  t*x*z + s*y],
                    [t*x*y + s*z,  t*y*y + c,    t*y*z - s*x],
                    [t*x*z - s*y,  t*y*z + s*x,  t*z*z + c]
                ])

                motion_transform = np.eye(4)
                motion_transform[:3, :3] = joint_rotation
            else:
                motion_transform = np.eye(4)

        elif joint.type == 'prismatic':
            # 移动关节：沿指定轴平移
            axis = np.array(joint.axis)
            if np.linalg.norm(axis) > 0:
                axis = axis / np.linalg.norm(axis)  # 归一化轴向量
                translation = axis * joint_value    # 计算平移向量

                motion_transform = np.eye(4)
                motion_transform[:3, 3] = translation
            else:
                motion_transform = np.eye(4)
        else:
            # 其他类型关节（如fixed）不产生运动
            motion_transform = np.eye(4)

        # 3. 组合原点变换和运动变换
        return np.dot(origin_transform, motion_transform)

    def get_link_transform(self, link_name: str) -> np.ndarray:
        """
        获取指定链接的变换矩阵

        Args:
            link_name (str): 链接名称

        Returns:
            np.ndarray: 4x4变换矩阵，如果链接不存在则返回单位矩阵
        """
        return self.link_transforms.get(link_name, np.eye(4))

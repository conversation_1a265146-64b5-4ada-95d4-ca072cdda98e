# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'gesture_edit.ui'
##
## Created by: Qt User Interface Compiler version 6.4.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (<PERSON><PERSON><PERSON>, Q<PERSON><PERSON>r, Q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>urs<PERSON>,
    <PERSON><PERSON><PERSON>, Q<PERSON>ontData<PERSON>, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QDialog, QFrame, QGridLayout,
    QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QScrollArea, QSizePolicy, QSpacerItem, QVBoxLayout,
    QWidget)

class Ui_Dialog(object):
    def setupUi(self, Dialog):
        if not Dialog.objectName():
            Dialog.setObjectName(u"Dialog")
        Dialog.resize(1114, 748)
        self.verticalLayout_4 = QVBoxLayout(Dialog)
        self.verticalLayout_4.setSpacing(10)
        self.verticalLayout_4.setObjectName(u"verticalLayout_4")
        self.verticalLayout_4.setContentsMargins(20, 20, 20, 20)
        self.horizontalLayout_2 = QHBoxLayout()
        self.horizontalLayout_2.setSpacing(0)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.lineEdit_name = QLineEdit(Dialog)
        self.lineEdit_name.setObjectName(u"lineEdit_name")
        sizePolicy = QSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lineEdit_name.sizePolicy().hasHeightForWidth())
        self.lineEdit_name.setSizePolicy(sizePolicy)

        self.horizontalLayout_2.addWidget(self.lineEdit_name)

        self.pushButton_name_edit = QPushButton(Dialog)
        self.pushButton_name_edit.setObjectName(u"pushButton_name_edit")

        self.horizontalLayout_2.addWidget(self.pushButton_name_edit)

        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer_3)

        self.pushButton_close = QPushButton(Dialog)
        self.pushButton_close.setObjectName(u"pushButton_close")

        self.horizontalLayout_2.addWidget(self.pushButton_close)


        self.verticalLayout_4.addLayout(self.horizontalLayout_2)

        self.horizontalLayout_3 = QHBoxLayout()
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.verticalLayout_2 = QVBoxLayout()
        self.verticalLayout_2.setSpacing(20)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.verticalLayout = QVBoxLayout()
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.label_urdf = QLabel(Dialog)
        self.label_urdf.setObjectName(u"label_urdf")
        self.label_urdf.setMinimumSize(QSize(733, 301))

        self.verticalLayout.addWidget(self.label_urdf)


        self.verticalLayout_2.addLayout(self.verticalLayout)

        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.pushButton_run = QPushButton(Dialog)
        self.pushButton_run.setObjectName(u"pushButton_run")

        self.horizontalLayout.addWidget(self.pushButton_run)

        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer)


        self.verticalLayout_2.addLayout(self.horizontalLayout)

        self.scrollArea_step = QScrollArea(Dialog)
        self.scrollArea_step.setObjectName(u"scrollArea_step")
        self.scrollArea_step.setWidgetResizable(True)
        self.scrollAreaWidgetContents = QWidget()
        self.scrollAreaWidgetContents.setObjectName(u"scrollAreaWidgetContents")
        self.scrollAreaWidgetContents.setGeometry(QRect(0, 0, 773, 251))
        self.gridLayout_2 = QGridLayout(self.scrollAreaWidgetContents)
        self.gridLayout_2.setSpacing(0)
        self.gridLayout_2.setObjectName(u"gridLayout_2")
        self.gridLayout_2.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_step = QGridLayout()
        self.gridLayout_step.setObjectName(u"gridLayout_step")
        self.gridLayout_step.setHorizontalSpacing(0)
        self.gridLayout_step.setVerticalSpacing(20)

        self.gridLayout_2.addLayout(self.gridLayout_step, 0, 0, 1, 1)

        self.scrollArea_step.setWidget(self.scrollAreaWidgetContents)

        self.verticalLayout_2.addWidget(self.scrollArea_step)

        self.horizontalLayout_4 = QHBoxLayout()
        self.horizontalLayout_4.setSpacing(100)
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.pushButton_reset = QPushButton(Dialog)
        self.pushButton_reset.setObjectName(u"pushButton_reset")

        self.horizontalLayout_4.addWidget(self.pushButton_reset)

        self.pushButton_save = QPushButton(Dialog)
        self.pushButton_save.setObjectName(u"pushButton_save")

        self.horizontalLayout_4.addWidget(self.pushButton_save)


        self.verticalLayout_2.addLayout(self.horizontalLayout_4)

        self.verticalLayout_2.setStretch(2, 1)

        self.horizontalLayout_3.addLayout(self.verticalLayout_2)

        self.frame_params = QFrame(Dialog)
        self.frame_params.setObjectName(u"frame_params")
        self.frame_params.setFrameShape(QFrame.StyledPanel)
        self.frame_params.setFrameShadow(QFrame.Raised)
        self.verticalLayout_3 = QVBoxLayout(self.frame_params)
        self.verticalLayout_3.setSpacing(0)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.verticalLayout_3.setContentsMargins(15, 20, 15, 20)
        self.gridLayout_params = QGridLayout()
        self.gridLayout_params.setObjectName(u"gridLayout_params")
        self.gridLayout_params.setHorizontalSpacing(15)
        self.gridLayout_params.setVerticalSpacing(20)
        self.gridLayout_params.setContentsMargins(-1, 20, -1, 20)
        self.pushButton_7 = QPushButton(self.frame_params)
        self.pushButton_7.setObjectName(u"pushButton_7")
        sizePolicy1 = QSizePolicy(QSizePolicy.Minimum, QSizePolicy.Expanding)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.pushButton_7.sizePolicy().hasHeightForWidth())
        self.pushButton_7.setSizePolicy(sizePolicy1)

        self.gridLayout_params.addWidget(self.pushButton_7, 0, 1, 1, 1)

        self.pushButton_8 = QPushButton(self.frame_params)
        self.pushButton_8.setObjectName(u"pushButton_8")
        sizePolicy1.setHeightForWidth(self.pushButton_8.sizePolicy().hasHeightForWidth())
        self.pushButton_8.setSizePolicy(sizePolicy1)

        self.gridLayout_params.addWidget(self.pushButton_8, 0, 2, 1, 1)

        self.pushButton_6 = QPushButton(self.frame_params)
        self.pushButton_6.setObjectName(u"pushButton_6")
        sizePolicy1.setHeightForWidth(self.pushButton_6.sizePolicy().hasHeightForWidth())
        self.pushButton_6.setSizePolicy(sizePolicy1)

        self.gridLayout_params.addWidget(self.pushButton_6, 0, 0, 1, 1)

        self.pushButton_9 = QPushButton(self.frame_params)
        self.pushButton_9.setObjectName(u"pushButton_9")
        sizePolicy1.setHeightForWidth(self.pushButton_9.sizePolicy().hasHeightForWidth())
        self.pushButton_9.setSizePolicy(sizePolicy1)

        self.gridLayout_params.addWidget(self.pushButton_9, 1, 0, 1, 1)

        self.pushButton_10 = QPushButton(self.frame_params)
        self.pushButton_10.setObjectName(u"pushButton_10")
        sizePolicy1.setHeightForWidth(self.pushButton_10.sizePolicy().hasHeightForWidth())
        self.pushButton_10.setSizePolicy(sizePolicy1)

        self.gridLayout_params.addWidget(self.pushButton_10, 1, 1, 1, 1)

        self.pushButton_11 = QPushButton(self.frame_params)
        self.pushButton_11.setObjectName(u"pushButton_11")
        sizePolicy1.setHeightForWidth(self.pushButton_11.sizePolicy().hasHeightForWidth())
        self.pushButton_11.setSizePolicy(sizePolicy1)

        self.gridLayout_params.addWidget(self.pushButton_11, 1, 2, 1, 1)


        self.verticalLayout_3.addLayout(self.gridLayout_params)


        self.horizontalLayout_3.addWidget(self.frame_params)


        self.verticalLayout_4.addLayout(self.horizontalLayout_3)


        self.retranslateUi(Dialog)

        QMetaObject.connectSlotsByName(Dialog)
    # setupUi

    def retranslateUi(self, Dialog):
        Dialog.setWindowTitle(QCoreApplication.translate("Dialog", u"Dialog", None))
        self.pushButton_name_edit.setText("")
        self.pushButton_close.setText("")
        self.label_urdf.setText(QCoreApplication.translate("Dialog", u"TextLabel", None))
        self.pushButton_run.setText("")
        self.pushButton_reset.setText(QCoreApplication.translate("Dialog", u"Reset", None))
        self.pushButton_save.setText(QCoreApplication.translate("Dialog", u"Save", None))
        self.pushButton_7.setText(QCoreApplication.translate("Dialog", u"PushButton", None))
        self.pushButton_8.setText(QCoreApplication.translate("Dialog", u"PushButton", None))
        self.pushButton_6.setText(QCoreApplication.translate("Dialog", u"PushButton", None))
        self.pushButton_9.setText(QCoreApplication.translate("Dialog", u"PushButton", None))
        self.pushButton_10.setText(QCoreApplication.translate("Dialog", u"PushButton", None))
        self.pushButton_11.setText(QCoreApplication.translate("Dialog", u"PushButton", None))
    # retranslateUi


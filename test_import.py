#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("测试导入 src...")
    from src import APP_ROOT_PATH
    print(f"APP_ROOT_PATH: {APP_ROOT_PATH}")
    
    print("测试导入 URDFViewer...")
    from src.gui.widgets.gl_widget.urdf_viewer import URDFViewer
    print("URDFViewer 导入成功")
    
    print("测试导入 GestureEditDialog...")
    from src.gui.widgets.gesture_edit_dialog import GestureEditDialog
    print("GestureEditDialog 导入成功")
    
    print("测试导入 GestureData...")
    from src.core.gesture_data_manager import GestureData, StepData
    print("GestureData 导入成功")
    
    print("所有导入测试通过！")
    
except Exception as e:
    print(f"导入错误: {e}")
    import traceback
    traceback.print_exc()

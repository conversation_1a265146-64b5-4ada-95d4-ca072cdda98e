from PySide6.QtCore import Qt
from PySide6.QtWidgets import QRadioButton


class CustomRadioButton(QRadioButton):
    def __init__(self, name, parent=None):
        super().__init__(name, parent)

        self.setCursor(Qt.PointingHandCursor)

        self.setStyleSheet("""
                QRadioButton {
                    color: #6F7577;
                    font-size: 20px;
                }
                
                QRadioButton::indicator {
                    width: 16px;
                    height: 16px;
                }
                
                QRadioButton::checked {
                    color: rgba(255, 255, 255, 1);
                }
                
                QRadioButton::unchecked {
                    color: rgba(255, 255, 255, 0.4);
                }
                
                QRadioButton::indicator:unchecked {
                    border: 1px solid white;
                    background-color: transparent;
                    border-radius: 8px;
                }

                QRadioButton::indicator:checked {
                    border: 1px solid #FF9429;
                    background-color: #FF9429;
                    border-radius: 8px;
                }
            """)


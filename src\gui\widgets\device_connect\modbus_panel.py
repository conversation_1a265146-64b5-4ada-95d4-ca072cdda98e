from PySide6.QtWidgets import QBoxLayout

from src.utils.serial_helper import serial_ports
from .base import ProtocolWidgetBase
from .param_widget import NumericParamWidget, SelectParamWidget
from ...components.base_widgets import BorderlessLayout


class ModbusPanel(ProtocolWidgetBase):
    def __init__(self, params_config: dict, parent=None):
        super().__init__(parent)

        self.id_range = params_config["id_range"]
        self.default_id = params_config["default_id"]
        self.baudrate_list = params_config["baudrate_list"]
        self.default_baudrate = params_config["default_baudrate"]

        self.setLayout(BorderlessLayout(QBoxLayout.Direction.TopToBottom))
        self.layout().setSpacing(7)

        id_info = params_config.get("id_tips", None)
        self.id_widget = NumericParamWidget("ID", self.id_range, self.default_id, id_info, self)
        self.layout().addWidget(self.id_widget)

        self.port_widget = SelectParamWidget("Port", [], "", self)
        self.layout().addWidget(self.port_widget)
        ports = serial_ports()
        for port_name in ports:
            self.port_widget.input.addItem(port_name)

        self.baudrate_widget = SelectParamWidget("Baudrate", self.baudrate_list, self.default_baudrate, self)
        self.layout().addWidget(self.baudrate_widget)

    def get_config(self):
        config = {
            "slave_address": self.id_widget.get_value(),
            "port": self.port_widget.get_value(),
            "baudrate": int(self.baudrate_widget.get_value()),
        }
        return config
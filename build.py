#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
PyInstaller 打包脚本
可以自定义打包参数
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

from src.config import VERSION


# 自定义打包参数 (可根据需要修改)
APP_NAME = "BrainCo_Revo_hand_tool"  # 应用名称
ICON_PATH = "resources/icons/revo2_hand.ico"  # 应用图标路径
ENTRY_FILE = "main.py"  # 入口文件
ONEFILE = False  # 修改为False，使用目录模式，避免资源提取问题
CONSOLE = True  # 是否显示控制台窗口
CLEAN_BUILD = True  # 是否清理之前的构建文件
OUTPUT_DIR = "dist"  # 输出目录
# NOARCHIVE 参数在某些版本中不支持，已移除

# 额外数据文件, 格式: [("源路径", "目标路径"), ...]
ADD_DATA = [
    # 确保包含资源目录，并保持相同的目录结构
    ("resources", "resources"),
    ("resources/images", "resources/images"),
    ("resources/icons", "resources/icons"),
    ("resources/fonts", "resources/fonts"),
]

# 额外二进制文件, 格式: [("源路径", "目标路径"), ...]
ADD_BINARY = [
    # 例如: ("lib/binary.dll", "lib"),
]

# 添加隐式导入的模块
HIDDEN_IMPORTS = [
    "PySide6.QtCore", 
    "PySide6.QtGui", 
    "PySide6.QtWidgets",
    "PySide6.QtSvg",  # 添加SVG支持
    "PySide6.QtSvgWidgets",  # 添加SVG支持
    "encodings",  # 添加编码模块，解决编码问题
    "encodings.utf_8",
    "encodings.ascii",
    "encodings.idna",
    "encodings.cp1252",  # Windows 默认编码
    "src.resources.resources_rc",  # 确保包含资源文件模块
    # 添加其他需要的隐式导入
]

# 排除的模块
EXCLUDE_MODULES = [
    # 例如: "matplotlib", "tkinter"
]


def build_app():
    """使用 PyInstaller 打包应用"""
    print(f"开始打包应用: {APP_NAME}")
    
    # 构建 PyInstaller 命令
    cmd = ["pyinstaller"]
    
    # 基本参数
    cmd.extend(["--name", APP_NAME])
    
    # 图标
    if os.path.exists(ICON_PATH):
        cmd.extend(["--icon", ICON_PATH])
    else:
        print(f"警告: 图标文件 {ICON_PATH} 不存在, 将使用默认图标")
    
    # 单文件/目录模式
    if ONEFILE:
        cmd.append("--onefile")
    else:
        cmd.append("--onedir")
    
    # 控制台窗口
    if CONSOLE:
        cmd.append("--console")
    else:
        cmd.append("--windowed")
    
    # 清理模式
    if CLEAN_BUILD:
        cmd.append("--clean")
    
    # 解决编码问题的其他参数
    cmd.append("--noupx")  # 不使用UPX压缩，可能会导致编码问题
    
    # 输出目录
    cmd.extend(["--distpath", OUTPUT_DIR])
    
    # 额外数据文件
    separator = ";" if sys.platform == "win32" else ":"
    for src, dst in ADD_DATA:
        # 确保源路径存在
        if os.path.exists(src):
            cmd.extend(["--add-data", f"{src}{separator}{dst}"])
        else:
            print(f"警告: 数据文件源路径不存在: {src}")
    
    # 额外二进制文件
    for src, dst in ADD_BINARY:
        cmd.extend(["--add-binary", f"{src}{separator}{dst}"])
    
    # 隐式导入
    for imp in HIDDEN_IMPORTS:
        cmd.extend(["--hidden-import", imp])
    
    # 排除模块
    for excl in EXCLUDE_MODULES:
        cmd.extend(["--exclude-module", excl])
    
    # 默认添加资源文件夹 (如果存在)
    if os.path.exists("resources"):
        cmd.extend(["--add-data", f"resources{separator}resources"])
    
    # 默认添加配置文件
    if os.path.exists("settings.yaml"):
        cmd.extend(["--add-data", f"settings.yaml{separator}."])
    
    # 创建spec文件而不是直接打包
    cmd.append("--log-level=DEBUG")
    
    # 入口文件
    cmd.append(ENTRY_FILE)
    
    # 执行打包命令
    print("执行打包命令:", " ".join(cmd))
    result = subprocess.run(cmd)
    
    if result.returncode == 0:
        print(f"\n打包完成! 输出目录: {OUTPUT_DIR}/{APP_NAME}")
        
        # 复制资源文件到输出目录的根目录，以防万一
        if not ONEFILE:
            output_app_dir = os.path.join(OUTPUT_DIR, APP_NAME)
            if os.path.exists("resources"):
                resources_dir = os.path.join(output_app_dir, "resources")
                if not os.path.exists(resources_dir):
                    os.makedirs(resources_dir, exist_ok=True)
                
                # 复制图像和图标
                for subdir in ["images", "icons", "fonts"]:
                    src_dir = os.path.join("src/resources", subdir)
                    if os.path.exists(src_dir):
                        dst_dir = os.path.join(resources_dir, subdir)
                        if not os.path.exists(dst_dir):
                            os.makedirs(dst_dir, exist_ok=True)
                        
                        for item in os.listdir(src_dir):
                            src_item = os.path.join(src_dir, item)
                            dst_item = os.path.join(dst_dir, item)
                            if os.path.isfile(src_item):
                                shutil.copy2(src_item, dst_item)
                                print(f"额外复制: {src_item} -> {dst_item}")
    else:
        print(f"\n打包失败! 错误代码: {result.returncode}")
        return False
    
    return True


def create_release_archive():
    """创建发布压缩包"""
    if ONEFILE:
        exe_path = os.path.join(OUTPUT_DIR, f"{APP_NAME}.exe")
    else:
        exe_dir = os.path.join(OUTPUT_DIR, APP_NAME)
        exe_path = exe_dir
    
    if not os.path.exists(exe_path):
        print(f"错误: 找不到打包后的文件: {exe_path}")
        return False
    
    # 创建发布文件夹
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    release_dir = f"release_{APP_NAME}_{VERSION}_{timestamp}"
    os.makedirs(release_dir, exist_ok=True)
    
    # 复制文件
    if ONEFILE:
        shutil.copy2(exe_path, os.path.join(release_dir, f"{APP_NAME}.exe"))
    else:
        shutil.copytree(exe_dir, os.path.join(release_dir, APP_NAME))
    
    # 复制说明文档
    if os.path.exists("README.md"):
        shutil.copy2("README.md", os.path.join(release_dir, "README.md"))
    
    print(f"\n发布文件已创建: {release_dir}")
    return True


if __name__ == "__main__":
    if build_app():
        create_release_archive() 
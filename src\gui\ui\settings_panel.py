# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'settings_panel.ui'
##
## Created by: Qt User Interface Compiler version 6.4.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, Q<PERSON><PERSON>al<PERSON>rad<PERSON>, Q<PERSON>ursor,
    Q<PERSON><PERSON>, QFontDatabase, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QGroupBox, QHBoxLayout, QPushButton,
    QSizePolicy, QSpacerItem, QVBoxLayout, QWidget)

class Ui_Form(object):
    def setupUi(self, Form):
        if not Form.objectName():
            Form.setObjectName(u"Form")
        Form.resize(1083, 621)
        sizePolicy = QSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(Form.sizePolicy().hasHeightForWidth())
        Form.setSizePolicy(sizePolicy)
        self.horizontalLayout = QHBoxLayout(Form)
        self.horizontalLayout.setSpacing(25)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.verticalLayout_2 = QVBoxLayout()
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.groupBox_device_name = QGroupBox(Form)
        self.groupBox_device_name.setObjectName(u"groupBox_device_name")

        self.verticalLayout_2.addWidget(self.groupBox_device_name)

        self.groupBox_device_info = QGroupBox(Form)
        self.groupBox_device_info.setObjectName(u"groupBox_device_info")
        sizePolicy1 = QSizePolicy(QSizePolicy.Preferred, QSizePolicy.Expanding)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.groupBox_device_info.sizePolicy().hasHeightForWidth())
        self.groupBox_device_info.setSizePolicy(sizePolicy1)

        self.verticalLayout_2.addWidget(self.groupBox_device_info)

        self.verticalSpacer = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.verticalLayout_2.addItem(self.verticalSpacer)


        self.horizontalLayout.addLayout(self.verticalLayout_2)

        self.verticalLayout_3 = QVBoxLayout()
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.verticalLayout = QVBoxLayout()
        self.verticalLayout.setSpacing(10)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.groupBox_notification = QGroupBox(Form)
        self.groupBox_notification.setObjectName(u"groupBox_notification")

        self.verticalLayout.addWidget(self.groupBox_notification)

        self.pushButton_restore_factory = QPushButton(Form)
        self.pushButton_restore_factory.setObjectName(u"pushButton_restore_factory")

        self.verticalLayout.addWidget(self.pushButton_restore_factory)


        self.verticalLayout_3.addLayout(self.verticalLayout)

        self.groupBox_limit_range = QGroupBox(Form)
        self.groupBox_limit_range.setObjectName(u"groupBox_limit_range")

        self.verticalLayout_3.addWidget(self.groupBox_limit_range)

        self.verticalSpacer_2 = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.verticalLayout_3.addItem(self.verticalSpacer_2)


        self.horizontalLayout.addLayout(self.verticalLayout_3)

        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer)


        self.retranslateUi(Form)

        QMetaObject.connectSlotsByName(Form)
    # setupUi

    def retranslateUi(self, Form):
        Form.setWindowTitle(QCoreApplication.translate("Form", u"Form", None))
        self.groupBox_device_name.setTitle(QCoreApplication.translate("Form", u"Device Name", None))
        self.groupBox_device_info.setTitle(QCoreApplication.translate("Form", u"Device_info", None))
        self.groupBox_notification.setTitle(QCoreApplication.translate("Form", u"Notification", None))
        self.pushButton_restore_factory.setText(QCoreApplication.translate("Form", u"Restore Factory Defaults", None))
        self.groupBox_limit_range.setTitle(QCoreApplication.translate("Form", u"FInger limit Range", None))
    # retranslateUi


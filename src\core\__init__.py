from bc_stark_sdk.main_mod import stark

from enum import Enum


class HandVersion(Enum):
    V2 = 1


class V2HandVersion(Enum):
    Basic = 1
    Touch = 2

    def to_sdk_type(self):
        mapping = {
            V2HandVersion.Basic: stark.StarkHardwareType.Revo2Basic,
            V2HandVersion.Touch: stark.StarkHardwareType.Revo2Touch,
        }
        return mapping.get(self)


class ProtocolType(Enum):
    Modbus = 1
    CanFd = 2
    EtherCAT = 3

    def to_sdk_type(self):
        mapping = {
            ProtocolType.Modbus: stark.StarkProtocolType.Modbus,
            ProtocolType.CanFd: stark.StarkProtocolType.CanFd,
            ProtocolType.EtherCAT: stark.StarkProtocolType.EtherCAT,
        }
        return mapping.get(self)


class Baudrate(Enum):
    Baud115200 = 115200
    Baud57600 = 57600
    Baud19200 = 19200
    Baud460800 = 460800
    Baud1000000 = 1000000
    Baud2000000 = 2000000
    Baud5000000 = 5000000

    def to_sdk_type(self):
        mapping = {
            Baudrate.Baud115200: stark.Baudrate.Baud115200,
            Baudrate.Baud57600: stark.Baudrate.Baud57600,
            Baudrate.Baud19200: stark.Baudrate.Baud19200,
            Baudrate.Baud460800: stark.Baudrate.Baud460800,
            Baudrate.Baud1000000: stark.Baudrate.Baud1Mbps,
            Baudrate.Baud2000000: stark.Baudrate.Baud2Mbps,
            Baudrate.Baud5000000: stark.Baudrate.Baud5Mbps,
        }
        return mapping.get(self)

    @classmethod
    def convert_to_sdk_type(cls, enum_value):
        """类方法 - 将任意枚举值转换为SDK类型"""
        if isinstance(enum_value, cls):
            return enum_value.to_sdk_type()
        return None


class FingerID(Enum):
    Thumb = 1
    ThumbAux = 2
    Index = 3
    Middle = 4
    Ring = 5
    Pinky = 6

    def to_sdk_type(self):
        mapping = {
            FingerID.Thumb: stark.FingerId.Thumb,
            FingerID.ThumbAux: stark.FingerId.ThumbAux,
            FingerID.Index: stark.FingerId.Index,
            FingerID.Middle: stark.FingerId.Middle,
            FingerID.Ring: stark.FingerId.Ring,
            FingerID.Pinky: stark.FingerId.Pinky,

        }
        return mapping.get(self)


class ActionSequenceId(Enum):
    DefaultGestureOpen = 1
    DefaultGestureFist = 2
    DefaultGesturePinchTwo = 3
    DefaultGesturePinchThree = 4
    DefaultGesturePinchSide = 5
    DefaultGesturePoint = 6

    CustomGesture1 = 10
    CustomGesture2 = 11
    CustomGesture3 = 12
    CustomGesture4 = 13
    CustomGesture5 = 14
    CustomGesture6 = 15
    CustomGesture7 = 16
    CustomGesture8 = 17
    CustomGesture9 = 18
    CustomGesture10 = 19
    CustomGesture11 = 20
    CustomGesture12 = 21
    CustomGesture13 = 22
    CustomGesture14 = 23
    CustomGesture15 = 24
    CustomGesture16 = 25
    CustomGesture17 = 26
    CustomGesture18 = 27
    CustomGesture19 = 28
    CustomGesture20 = 29
    CustomGesture21 = 30
    CustomGesture22 = 7
    CustomGesture23 = 8
    CustomGesture24 = 9

    def to_sdk_type(self):
        mapping = {
            ActionSequenceId.DefaultGestureOpen: stark.ActionSequenceId.DefaultGestureOpen,
            ActionSequenceId.DefaultGestureFist: stark.ActionSequenceId.DefaultGestureFist,
            ActionSequenceId.DefaultGesturePinchTwo: stark.ActionSequenceId.DefaultGesturePinchTwo,
            ActionSequenceId.DefaultGesturePinchThree: stark.ActionSequenceId.DefaultGesturePinchThree,
            ActionSequenceId.DefaultGesturePinchSide: stark.ActionSequenceId.DefaultGesturePinchSide,
            ActionSequenceId.DefaultGesturePoint: stark.ActionSequenceId.DefaultGesturePoint,

            ActionSequenceId.CustomGesture1: stark.ActionSequenceId.CustomGesture1,
            ActionSequenceId.CustomGesture2: stark.ActionSequenceId.CustomGesture2,
            ActionSequenceId.CustomGesture3: stark.ActionSequenceId.CustomGesture3,
            ActionSequenceId.CustomGesture4: stark.ActionSequenceId.CustomGesture4,
            ActionSequenceId.CustomGesture5: stark.ActionSequenceId.CustomGesture5,
            ActionSequenceId.CustomGesture6: stark.ActionSequenceId.CustomGesture6,
            ActionSequenceId.CustomGesture7: stark.ActionSequenceId.CustomGesture7,
            ActionSequenceId.CustomGesture8: stark.ActionSequenceId.CustomGesture8,
            ActionSequenceId.CustomGesture9: stark.ActionSequenceId.CustomGesture9,
            ActionSequenceId.CustomGesture10: stark.ActionSequenceId.CustomGesture10,
            ActionSequenceId.CustomGesture11: stark.ActionSequenceId.CustomGesture11,
            ActionSequenceId.CustomGesture12: stark.ActionSequenceId.CustomGesture12,
            ActionSequenceId.CustomGesture13: stark.ActionSequenceId.CustomGesture13,
            ActionSequenceId.CustomGesture14: stark.ActionSequenceId.CustomGesture14,
            ActionSequenceId.CustomGesture15: stark.ActionSequenceId.CustomGesture15,
            ActionSequenceId.CustomGesture16: stark.ActionSequenceId.CustomGesture16,
            ActionSequenceId.CustomGesture17: stark.ActionSequenceId.CustomGesture17,
            ActionSequenceId.CustomGesture18: stark.ActionSequenceId.CustomGesture18,
            ActionSequenceId.CustomGesture19: stark.ActionSequenceId.CustomGesture19,
            ActionSequenceId.CustomGesture20: stark.ActionSequenceId.CustomGesture20,
            ActionSequenceId.CustomGesture21: stark.ActionSequenceId.CustomGesture21,
            ActionSequenceId.CustomGesture22: stark.ActionSequenceId.CustomGesture22,
            ActionSequenceId.CustomGesture23: stark.ActionSequenceId.CustomGesture23,
            ActionSequenceId.CustomGesture24: stark.ActionSequenceId.CustomGesture24,
        }
        return mapping.get(self)


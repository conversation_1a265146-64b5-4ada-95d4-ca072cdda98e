from PySide6.QtCore import Signal, Qt, QRect
from PySide6.QtGui import <PERSON><PERSON><PERSON>ter, QPainterPath, QBrush, QColor, QPen
from PySide6.QtWidgets import QWidget, QFrame, QHBoxLayout, QSlider, QVBoxLayout, QLabel, QPushButton, QSizePolicy, \
    QAbstractSpinBox
from ..ui.finger_control import Ui_Form as Finger_Control_Ui
from ..ui_config import ControlType
from ...core import FingerID
from ...utils.public_funcs import hex_to_rgb


class CustomButton(QPushButton):
    def __init__(self, text, back_color, color, parent=None):
        super().__init__(text, parent)

        self.setFixedSize(320, 60)

        self.setStyleSheet(f"""
            font-family: 'Alibaba PuHuiTi 2.0';
            font-weight: 300;
            font-size: 20px;
            background-color: {back_color};
            color: {color};
            border: none;
            border-radius: 8px;
            padding: 20, 16, 20, 16;
            """)


class FingerControlWidget(QFrame, Finger_Control_Ui):
    slider_value_changed_signal = Signal(FingerID, int)

    value_changed_signal = Signal(FingerID, str, int)

    def __init__(self, finger_id, finger_name, joint_name, dot_color, slider_direction, bg_color=None, spx_color=None, parent=None):
        super().__init__(parent)
        self.setupUi(self)

        self.finger_id = finger_id
        self.finger_name = finger_name

        html = f"""
        <div style="display: flex; align-items: center; height: 100%; text-align: left;">
            <span style="font-size:12pt;">{finger_name}</span>
            <span style="font-size:9pt; margin-left: 4px;">{joint_name}</span>
        </div>
        """
        self.textEdit_name.setHtml(html)
        self.textEdit_name.setStyleSheet("background-color: transparent; color: white; border: none")

        color = "#FFFFFF" if spx_color is None else spx_color
        rgba_color = hex_to_rgb(color)
        spinbox_style = f"""
            
            QDoubleSpinBox::enabled {{
                background-color: rgba({rgba_color[0]}, {rgba_color[0]}, {rgba_color[0]}, 50); 
                color: white; 
                border: none; 
                border-radius: 7px; 
                font-size: 22px;
                padding: 2px 5px;
            }}
            
            QDoubleSpinBox::disabled {{
                background-color: rgba({rgba_color[0]}, {rgba_color[0]}, {rgba_color[0]}, 10); 
                color: white; 
                border: none; 
                border-radius: 7px; 
                font-size: 22px;
                padding: 2px 5px;
            }}

            QDoubleSpinBox QLineEdit {{
                padding: 0px;
                margin: 0px;
            }}
        """

        self.doubleSpinBox_speed.setStyleSheet(spinbox_style)
        self.doubleSpinBox_force.setStyleSheet(spinbox_style)
        self.doubleSpinBox_position.setStyleSheet(spinbox_style)
        self.doubleSpinBox_position.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.doubleSpinBox_speed.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.doubleSpinBox_force.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)

        self.toolButton_dot.setStyleSheet(f"background-color: {dot_color};border: none; border-radius: 7px;")

        self.doubleSpinBox_position.setContentsMargins(2, 2, 2, 2)

        if slider_direction == "right":
            self.slider = self.frame_slider_right
            self.frame_slider_bottom.hide()
            self.frame_zw.setMinimumHeight(36)
        else:
            self.slider = self.frame_slider_bottom
            self.frame_slider_right.hide()
            self.frame_zw.hide()

        # self.setMinimumHeight(280)

        color = "#2B2E35" if bg_color is None else bg_color
        self.setStyleSheet(f"background-color: {color}; border-radius: 7px;")

        self.doubleSpinBox_position.valueChanged.connect(self._on_doubleSpinBox_position_value_changed)
        self.slider.slider_container.valueChanged.connect(self._on_slider_value_changed)

        self.doubleSpinBox_position.valueChanged.connect(lambda value: self.value_changed_signal.emit(self.finger_id, "position", int(value)))
        self.doubleSpinBox_speed.valueChanged.connect(lambda value: self.value_changed_signal.emit(self.finger_id, "speed", int(value)))
        self.doubleSpinBox_force.valueChanged.connect(lambda value: self.value_changed_signal.emit(self.finger_id, "force", int(value)))

        self.slider.slider_container.value_changed.connect(lambda value: self.slider_value_changed_signal.emit(self.finger_id, int(value)))

    def reset_spinbox_enabled(self, control_type: ControlType):
        self.doubleSpinBox_position.setEnabled(control_type == ControlType.POSITION or control_type == ControlType.POSITION_SPEED)
        self.doubleSpinBox_speed.setEnabled(control_type == ControlType.SPEED or control_type == ControlType.POSITION_SPEED)
        self.doubleSpinBox_force.setEnabled(control_type == ControlType.FORCE)

        self.slider.setEnabled(control_type == ControlType.POSITION)

    def _on_slider_value_changed(self, value):
        self.doubleSpinBox_position.valueChanged.disconnect(self._on_doubleSpinBox_position_value_changed)
        self.doubleSpinBox_position.setValue(value)
        self.doubleSpinBox_position.valueChanged.connect(self._on_doubleSpinBox_position_value_changed)

    def _on_doubleSpinBox_position_value_changed(self, value):
        self.slider.slider_container.valueChanged.disconnect(self._on_slider_value_changed)
        self.slider.slider_container.setValue(value)
        self.slider.slider_container.valueChanged.connect(self._on_slider_value_changed)

    def _disconnect_all_widget(self):
        self.slider.slider_container.valueChanged.disconnect(self._on_slider_value_changed)
        self.doubleSpinBox_position.valueChanged.disconnect(self._on_doubleSpinBox_position_value_changed)

    def _connect_all_widget(self):
        self.slider.slider_container.valueChanged.connect(self._on_slider_value_changed)
        self.doubleSpinBox_position.valueChanged.connect(self._on_doubleSpinBox_position_value_changed)

    def set_value(self, position, speed, force):
        self._disconnect_all_widget()

        # 设置滑块和输入框的值
        self.slider.slider_container.setValue(position)
        self.doubleSpinBox_position.setValue(position)
        self.doubleSpinBox_speed.setValue(speed)
        self.doubleSpinBox_force.setValue(force)

        # 重新连接信号
        self._connect_all_widget()

    def get_value(self):
        return self.doubleSpinBox_position.value(), self.doubleSpinBox_speed.value(), self.doubleSpinBox_force.value()
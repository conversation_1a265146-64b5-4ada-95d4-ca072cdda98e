#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import subprocess
from pathlib import Path


def generate_qrc_file(resources_dir, output_qrc_file):
    """
    遍历resources目录，生成对应的.qrc文件

    Args:
        resources_dir: resources文件夹路径
        output_qrc_file: 输出的.qrc文件路径
    """
    print(f"正在扫描资源目录: {resources_dir}")

    # 确保resources目录存在
    if not os.path.exists(resources_dir):
        print(f"错误: 资源目录 '{resources_dir}' 不存在!")
        return False

    # 开始生成qrc文件内容
    qrc_content = '<!DOCTYPE RCC>\n<RCC version="1.0">\n'
    qrc_content += '  <qresource>\n'

    # 遍历resources目录及其子目录
    for root, dirs, files in os.walk(resources_dir):
        for file in files:
            # 忽略.DS_Store等隐藏文件
            if file.startswith('.'):
                continue
                
            # 忽略已存在的qrc文件和resources_rc.py文件
            if file.endswith('.qrc') or file == 'resources_rc.py':
                continue

            # 获取相对于resources目录的路径
            full_path = os.path.join(root, file)
            
            # 修改：获取相对于resources_dir的路径，而不是其父目录
            rel_path = os.path.relpath(full_path, resources_dir)

            # 将Windows路径分隔符转换为正斜杠(/)
            rel_path = rel_path.replace('\\', '/')

            # 添加到qrc文件
            qrc_content += f'    <file>{rel_path}</file>\n'
            print(f"添加资源: {rel_path}")

    qrc_content += '  </qresource>\n'
    qrc_content += '</RCC>\n'

    # 写入qrc文件
    with open(output_qrc_file, 'w', encoding='utf-8') as f:
        f.write(qrc_content)

    print(f"已生成qrc文件: {output_qrc_file}")
    return True


def generate_py_from_qrc(qrc_file, output_py_file):
    """
    使用pyside6-rcc将.qrc文件转换为.py文件

    Args:
        qrc_file: 输入的.qrc文件路径
        output_py_file: 输出的.py文件路径
    """
    try:
        # 确保qrc_file是绝对路径
        qrc_file_abs = os.path.abspath(qrc_file)
        output_py_file_abs = os.path.abspath(output_py_file)
        
        # 尝试使用pyside6-rcc
        cmd = ['pyside6-rcc', qrc_file_abs, '-o', output_py_file_abs]
        print(f"执行命令: {' '.join(cmd)}")

        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"已生成Python资源文件: {output_py_file}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: 执行pyside6-rcc失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    except FileNotFoundError:
        # 如果找不到pyside6-rcc，尝试使用pyrcc5
        try:
            cmd = ['pyrcc5', qrc_file_abs, '-o', output_py_file_abs]
            print(f"pyside6-rcc未找到，尝试使用pyrcc5")
            print(f"执行命令: {' '.join(cmd)}")

            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(f"已生成Python资源文件: {output_py_file}")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError) as e:
            print(f"错误: 执行pyrcc5也失败: {e}")
            print("请确保已安装PySide6或PyQt5，并且pyside6-rcc或pyrcc5在系统PATH中")
            return False


def main():
    # 获取当前脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 使用绝对路径定义资源目录、qrc文件和py文件的路径
    resources_dir = os.path.abspath(os.path.join(script_dir, 'resources'))
    qrc_file = os.path.abspath(os.path.join(resources_dir, 'resources.qrc'))
    py_file = os.path.abspath(os.path.join(resources_dir, 'resources_rc.py'))

    print("=== 资源文件生成工具 ===")
    print(f"资源目录: {resources_dir}")
    print(f"QRC文件: {qrc_file}")
    print(f"输出Python文件: {py_file}")
    print("========================")

    # 生成qrc文件
    if generate_qrc_file(resources_dir, qrc_file):
        # 生成py文件
        generate_py_from_qrc(qrc_file, py_file)

    print("处理完成!")


if __name__ == "__main__":
    main()
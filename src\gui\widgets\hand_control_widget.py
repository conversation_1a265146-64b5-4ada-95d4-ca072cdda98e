from PySide6.Qt<PERSON>ore import Signal, Qt, QRect
from PySide6.QtGui import <PERSON><PERSON><PERSON><PERSON>, QPainterPath, QBrush, QColor, QPen
from PySide6.QtWidgets import QWidget, QFrame, QHBoxLayout, QSlider, QVBoxLayout, QLabel, QPushButton, QSizePolicy, \
    QAbstractSpinBox

from ..components.finger_control_widget import FingerControlWidget
from ..ui.finger_control import Ui_Form as Finger_Control_Ui
from ..ui_config import ControlType
from ...core import FingerID


class CustomButton(QPushButton):
    def __init__(self, text, back_color, color, parent=None):
        super().__init__(text, parent)

        self.setFixedSize(320, 60)

        self.setStyleSheet(f"""
            font-family: 'Alibaba PuHuiTi 2.0';
            font-weight: 300;
            font-size: 20px;
            background-color: {back_color};
            color: {color};
            border: none;
            border-radius: 8px;
            padding: 20, 16, 20, 16;
            """)


class HandControlWidget(QWidget):
    slider_value_changed_signal = Signal(FingerID, int)

    def __init__(self, parent=None):
        super().__init__(parent)

        self.main_layout = QVBoxLayout()
        self.main_layout.setSpacing(15)
        self.setLayout(self.main_layout)
        self.setContentsMargins(0, 0, 0, 0)

        h_layout = QHBoxLayout()
        self.thumb_1_control = FingerControlWidget(FingerID.Thumb, "Thumb", "Fl/Ex", "#78BB07", "right")
        self.thumb_2_control = FingerControlWidget(FingerID.ThumbAux, "Thumb", "Ad/Ab", "#4454C6", "bottom")
        self.index_control = FingerControlWidget(FingerID.Index, "Index", "Fl/Ex", "#F0A501", "right")
        self.middle_control = FingerControlWidget(FingerID.Middle, "Middle", "Fl/Ex", "#E63D45", "right")
        self.ring_control = FingerControlWidget(FingerID.Ring, "Ring", "Fl/Ex", "#5098BD", "right")
        self.pinky_control = FingerControlWidget(FingerID.Pinky, "Pinky", "Fl/Ex", "#008545", "right")
        h_layout.addWidget(self.thumb_2_control)
        h_layout.addWidget(self.thumb_1_control)
        h_layout.addWidget(self.index_control)
        h_layout.addWidget(self.middle_control)
        h_layout.addWidget(self.ring_control)
        h_layout.addWidget(self.pinky_control)
        self.main_layout.addLayout(h_layout)

        h_layout = QHBoxLayout()
        self.apply_all_button = CustomButton(text="Apply All", back_color="#FF9429", color="black")
        self.set_gesture_zero_button = CustomButton(text=self.tr("Manual Calibration"), back_color="#3F4749", color="white")
        self.set_packing_gestures_button = CustomButton(text=self.tr("Packing Gesture"), back_color="#3F4749", color="white")
        self.add_gesture_button = QPushButton(self.tr("+ Add to Gesture Library"))
        self.add_gesture_button.setFixedSize(320, 60)
        self.add_gesture_button.setStyleSheet(f"""
            font-family: 'Alibaba PuHuiTi 2.0';
            font-weight: 300;
            font-size: 20px;
            background-color: transparent;
            color: white;
            border: 1px solid #FF9429;
            border-radius: 8px;
            padding: 20, 16, 20, 16;
            """)
        h_layout.addWidget(self.apply_all_button)
        # h_layout.addWidget(self.add_gesture_button)
        h_layout.addStretch()
        h_layout.addWidget(self.set_gesture_zero_button)
        h_layout.addWidget(self.set_packing_gestures_button)
        self.main_layout.addLayout(h_layout)

        self.connect_event()

    def set_input_field_range(self, min_positions, max_positions, min_speeds, max_speeds, min_currents, max_currents):
        for idx, spinbox in enumerate([self.thumb_1_control, self.thumb_2_control, self.index_control,
                        self.middle_control, self.ring_control, self.pinky_control]):
            # spinbox.change_widget_range("percentage")
            spinbox.doubleSpinBox_position.setRange(min_positions[idx], max_positions[idx])
            spinbox.doubleSpinBox_speed.setRange(min_speeds[idx], max_speeds[idx])
            spinbox.doubleSpinBox_force.setRange(min_currents[idx], max_currents[idx])

            spinbox.slider.slider_container.setRange(min_positions[idx], max_positions[idx])

    def slider_value_changed(self, value):
        pass

    def connect_event(self):
        for slider in [self.thumb_1_control, self.thumb_2_control, self.index_control,
                       self.middle_control, self.ring_control, self.pinky_control]:
            slider.slider_value_changed_signal.connect(self.slider_value_changed_signal.emit)

    def set_finger_controls_enabled_by_typed(self, control_type: ControlType):
        for control in [self.thumb_1_control, self.thumb_2_control, self.index_control,
                        self.middle_control, self.ring_control, self.pinky_control]:
            control.reset_spinbox_enabled(control_type)

    def resizeEvent(self, event):
        super().resizeEvent(event)




# if __name__ == '__main__':
#     import sys
#     from PySide6.QtWidgets import QApplication
#     app = QApplication(sys.argv)
#
#     # 创建自定义窗口
#     window = HandControlWidget()
#
#
#     # 显示窗口
#     window.show()
#
#     sys.exit(app.exec())



from PySide6.QtWidgets import <PERSON>W<PERSON><PERSON>, QFrame, QPushButton, QDialog
from PySide6.QtCore import Qt, Signal

from ..ui.alert_widget import Ui_Form


class AlertWidget(QFrame, Ui_Form):
    button_clicked = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUi(self)
        self.resize(622, 363)

        self._init_ui()
        self._connect_signals()

    def _init_ui(self):
        # 设置窗口弹窗属性, 无边框
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        # self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setContentsMargins(40, 20, 40, 20)
        self.layout().setSpacing(20)
        
        self.setStyleSheet("""
            QFrame {
                background-color: #27282D;
                border-radius: 16px;
            }
            QPushButton {
                background-color: #27282D;
                color: #FF9429;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
                font-family: 'Alibaba PuHuiTi 2.0';
                font-size: 24px;
            }
        """)

        self.label_title.setStyleSheet("font-size: 24px; color: white; font-family: 'Alibaba PuHuiTi 2.0';font-weight: 400;")
        self.textEdit_tips.setStyleSheet("font-size: 18px; color: gray;font-family: 'Alibaba PuHuiTi 2.0';font-weight: 400;")
        self.textEdit_tips.setReadOnly(True)

        self.pushButton.resize(120, 34)
        self.pushButton.setCursor(Qt.CursorShape.PointingHandCursor)

        self.label_img.setFixedSize(52, 52)
        self.label_img.setScaledContents(True)

    def _connect_signals(self):
        self.pushButton.clicked.connect(self.close)
        # self.pushButton.clicked.connect(self.button_clicked.emit)

    def set_image(self, pixmap):
        self.label_img.setPixmap(pixmap)
        
    def set_title(self, title):
        self.label_title.setText(title)
        
    def set_message(self, message):
        self.textEdit_tips.setText(message)
        
    def set_button_text(self, text):
        self.pushButton.setText(text)
        
    def show_alert(self, title="", message="", button_text="确定"):
        self.set_title(title)
        self.set_message(message)
        self.set_button_text(button_text)
        self.show()


class QuestionSaveWidget(QDialog, Ui_Form):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUi(self)
        self.resize(622, 363)
        self.need_save = True
        self._init_ui()
        self._connect_signals()

    def _connect_signals(self):
        self.pushButton.clicked.connect(self.close)
        # self.pushButton.clicked.connect(self.button_clicked.emit)

    def _init_ui(self):
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setContentsMargins(40, 20, 40, 20)
        self.layout().setSpacing(20)

        self.pushButton_exit = QPushButton(self)
        self.pushButton_exit.setCursor(Qt.CursorShape.PointingHandCursor)
        self.horizontalLayout_3.insertWidget(1, self.pushButton_exit)
        self.horizontalLayout_3.setSpacing(50)

        self.pushButton_exit.clicked.connect(self.close_without_saving)

        self.setStyleSheet("""
            QFrame {
                background-color: #27282D;
                border-radius: 16px;
            }
        """)

        self.textEdit_tips.setReadOnly(True)
        self.pushButton.setStyleSheet("background-color: transparent;color: #FF9429;border: none;font-size: 24px;")
        self.pushButton_exit.setStyleSheet("background-color: transparent; color: rgba(255, 255, 255, 125);border: none; font-size: 24px;")

        self.label_title.setStyleSheet("font-size: 24px; color: white;font-weight: 400; background-color: transparent;")
        self.textEdit_tips.setStyleSheet("font-size: 24px; color: rgba(255, 255, 255, 125); background-color: transparent;")

        self.pushButton.resize(120, 34)
        self.pushButton.setCursor(Qt.CursorShape.PointingHandCursor)
        self.label_img.setFixedSize(52, 52)
        self.label_img.setScaledContents(True)

    def close_without_saving(self):
        self.need_save = False
        self.close()

    def show_alert(self, title="", message="", save_button_text="", exit_button_text="", pixmap=None):
        self.label_title.setText(title)
        self.textEdit_tips.setText(message)
        self.pushButton.setText(save_button_text)
        self.pushButton_exit.setText(exit_button_text)
        if pixmap:
            self.label_img.setPixmap(pixmap)

        self.exec()



if __name__ == '__main__':
    import sys
    from PySide6.QtWidgets import QApplication, QLabel
    app = QApplication(sys.argv)

    window = AlertWidget()
    window.show()

    sys.exit(app.exec())
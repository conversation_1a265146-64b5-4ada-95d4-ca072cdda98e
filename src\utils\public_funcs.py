

def hex_to_rgb(hex_color: str) -> tuple[int, int, int]:
    """
    把 #RRGGBB 格式的十六进制颜色转换为 RGB 元组 (r, g, b)。

    参数:
        hex_color: 字符串，形如 "#336699" 或 "336699"

    返回:
        (r, g, b) 三元组，每个值范围 0~255
    """
    hex_color = hex_color.lstrip('#')  # 去掉开头的 '#'
    if len(hex_color) != 6:
        raise ValueError("颜色代码必须是6位十六进制数")

    r = int(hex_color[0:2], 16)
    g = int(hex_color[2:4], 16)
    b = int(hex_color[4:6], 16)
    return r, g, b


print(hex_to_rgb("#4E5255"))
import glob
import sys

import serial


from serial.tools import list_ports


def serial_ports():
    if sys.platform.startswith('win'):
        ports = list_ports.comports()
        result = [port.device for port in ports if 'USB' in port.description]
    elif sys.platform.startswith('linux') or sys.platform.startswith('cygwin'):
        ports = glob.glob('/dev/tty[A-Za-z]*')
        ports = [port for port in ports if 'USB' in port]
        result = []
        for port in ports:
            try:
                s = serial.Serial(port)
                s.close()
                result.append(port)
            except (OSError, serial.SerialException):
                pass
    elif sys.platform.startswith('darwin'):
        ports = glob.glob('/dev/tty.*')
        ports = [port for port in ports if 'usb' in port.lower()]
        result = []
        for port in ports:
            try:
                s = serial.Serial(port)
                s.close()
                result.append(port)
            except (OSError, serial.SerialException):
                pass
    else:
        raise EnvironmentError('Unsupported platform')

    return result

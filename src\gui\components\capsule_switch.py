from PySide6.QtCore import Qt, QRect, QPropertyAnimation, Property, Signal, QEasingCurve, QSize
from PySide6.QtGui import QPainter, QColor, QPen, QBrush
from PySide6.QtWidgets import QWidget


class CapsuleSwitch(QWidget):
    toggled = Signal(bool)  # Signal emitted when switch is toggled

    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Default size
        self.setMinimumWidth(60)
        self.setMinimumHeight(28)
        
        # State
        self._checked = False
        self._thumb_position = 0.0
        
        # Setup animation
        self._animation = QPropertyAnimation(self, b"thumbPosition")
        self._animation.setEasingCurve(QEasingCurve.InOutCubic)
        self._animation.setDuration(200)  # Animation duration in ms
        
        # Colors
        self._on_color = QColor("#FF9429")           # 蓝色背景（开启状态）
        self._off_color = QColor("#FFFFFF")          # 白色背景（关闭状态）
        self._thumb_on_color = QColor("#FFFFFF")     # 开启状态下的圆球颜色（白色）
        self._thumb_off_color = QColor("#CCCCCC")    # 关闭状态下的圆球颜色（灰色）
        
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Calculate metrics
        rect = self.rect()
        capsule_height = rect.height()
        capsule_width = rect.width()
        
        radius = capsule_height // 2
        
        # Draw the capsule background without border
        background_color = self._on_color if self._checked else self._off_color
        painter.setPen(Qt.NoPen)  # 设置为无边框
        painter.setBrush(QBrush(background_color))
        painter.drawRoundedRect(rect, radius, radius)
        
        # Draw the moving thumb with different color based on state
        max_travel = capsule_width - capsule_height
        thumb_x = int(self._thumb_position * max_travel) + 2
        thumb_rect = QRect(thumb_x, 2, capsule_height - 4, capsule_height - 4)
        
        # 根据开关状态选择圆球颜色
        thumb_color = self._thumb_on_color if self._checked else self._thumb_off_color
        
        painter.setPen(Qt.NoPen)
        painter.setBrush(QBrush(thumb_color))
        painter.drawEllipse(thumb_rect)
        
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.setChecked(not self._checked)
            self.toggled.emit(self._checked)
            
    def resizeEvent(self, event):
        # Ensure animation is properly set up after resize
        self.updateAnimation()
        
    def updateAnimation(self):
        # Make sure animation is properly configured
        if self._checked:
            self._animation.setStartValue(0.0)
            self._animation.setEndValue(1.0)
        else:
            self._animation.setStartValue(1.0)
            self._animation.setEndValue(0.0)
        
    def setChecked(self, checked):
        if self._checked != checked:
            self._checked = checked
            
            # Configure animation based on new state
            self._animation.stop()
            if checked:
                self._animation.setStartValue(0.0)
                self._animation.setEndValue(1.0)
            else:
                self._animation.setStartValue(1.0)
                self._animation.setEndValue(0.0)
                
            self._animation.start()
            self.update()
    
    # 设置开启状态下的背景颜色
    def setOnColor(self, color):
        if isinstance(color, str):
            self._on_color = QColor(color)
        elif isinstance(color, QColor):
            self._on_color = color
        self.update()
    
    # 设置关闭状态下的背景颜色
    def setOffColor(self, color):
        if isinstance(color, str):
            self._off_color = QColor(color)
        elif isinstance(color, QColor):
            self._off_color = color
        self.update()
    
    # 设置开启状态下圆球颜色
    def setThumbOnColor(self, color):
        if isinstance(color, str):
            self._thumb_on_color = QColor(color)
        elif isinstance(color, QColor):
            self._thumb_on_color = color
        self.update()
    
    # 设置关闭状态下圆球颜色
    def setThumbOffColor(self, color):
        if isinstance(color, str):
            self._thumb_off_color = QColor(color)
        elif isinstance(color, QColor):
            self._thumb_off_color = color
        self.update()
            
    def isChecked(self):
        return self._checked
        
    def thumbPosition(self):
        return self._thumb_position
        
    def setThumbPosition(self, pos):
        if self._thumb_position != pos:
            self._thumb_position = pos
            self.update()
        
    # Define the property for animation
    thumbPosition = Property(float, thumbPosition, setThumbPosition)
    
    def sizeHint(self):
        return QSize(60, 28)


if __name__ == "__main__":
    # Example usage
    import sys
    from PySide6.QtWidgets import QApplication, QVBoxLayout, QLabel, QHBoxLayout, QPushButton
    
    app = QApplication(sys.argv)
    
    # Create window with switch
    window = QWidget()
    layout = QVBoxLayout(window)
    
    # Add a switch
    switch = CapsuleSwitch()
    label = QLabel("开关状态：关闭")
    
    # Connect to the toggled signal
    def on_switch_toggled(checked):
        label.setText("开关状态：打开" if checked else "开关状态：关闭")
    
    switch.toggled.connect(on_switch_toggled)
    
    # 添加按钮来改变圆球颜色
    btn_layout = QHBoxLayout()
    
    btn_red = QPushButton("红色圆球")
    btn_red.clicked.connect(lambda: switch.setThumbOnColor("#FF5555"))
    
    btn_green = QPushButton("绿色圆球")
    btn_green.clicked.connect(lambda: switch.setThumbOnColor("#55FF55"))
    
    btn_blue = QPushButton("蓝色圆球")
    btn_blue.clicked.connect(lambda: switch.setThumbOnColor("#5555FF"))
    
    btn_layout.addWidget(btn_red)
    btn_layout.addWidget(btn_green)
    btn_layout.addWidget(btn_blue)
    
    layout.addWidget(switch)
    layout.addWidget(label)
    layout.addLayout(btn_layout)
    
    window.setWindowTitle("胶囊开关示例")
    window.resize(300, 200)
    window.show()
    
    sys.exit(app.exec()) 
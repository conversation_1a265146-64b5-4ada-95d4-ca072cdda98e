# -*- coding: utf-8 -*-
import os

import oss2
import json

import requests
from oss2.credentials import EnvironmentVariableCredentialsProvider


# 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。

# OSS_ACCESS_KEY_ID: LTAI5tSTPH2x2MWQSMYCJuxt
# OSS_ACCESS_KEY_SECRET: ******************************

auth = oss2.ProviderAuth(EnvironmentVariableCredentialsProvider())
endpoint = 'oss-cn-beijing.aliyuncs.com'
bucket_name = 'brainco-app'
bucket = oss2.Bucket(auth, f'https://{endpoint}', bucket_name)


def upload(sku: str, fw_file_path: str, fw_version: str):
    with open(fw_file_path, 'rb') as f:
        file_name = os.path.split(fw_file_path)[1]

        if "test" in file_name:
            if str(file_name).endswith("hex"):
                prefix = f'mobius/firmware/Factory/{sku}/test/hex/'
            else:
                prefix = f'mobius/firmware/Factory/{sku}/test/ota/'
        else:
            prefix = f'mobius/firmware/Factory/{sku}/user/'
        fileKey = f'{prefix}{file_name}'
        versionKey = f'{prefix}upgrade_release.json'
        print("https://brainco-app.oss-cn-beijing.aliyuncs.com/" + versionKey)
        bucket.put_object(fileKey, f)

        fileUrl = f'https://{bucket_name}.{endpoint}/{fileKey}'
        versionContent = {
            'version': fw_version,
            'showOnStart': False,
            'force': False,
            'url': fileUrl
        }
        bucket.put_object(versionKey, json.dumps(versionContent))


# 查询服务器上面的最新版本
def get_new_fw_version(sku: str, is_test_fw: bool, test_fw_type="hex"):
    assert test_fw_type in ["hex", "ota"], "test_fw_type must be hex or ota"
    if is_test_fw:
        url = f"https://brainco-app.oss-cn-beijing.aliyuncs.com/mobius/firmware/Factory/{sku}/test/{test_fw_type}/upgrade_release.json"
    else:
        url = f"https://brainco-app.oss-cn-beijing.aliyuncs.com/mobius/firmware/Factory/{sku}/user/upgrade_release.json"
    response = requests.get(url)
    response.raise_for_status()  # 当请求失败时抛出异常
    json_data = json.loads(response.text)
    print(json_data)
    new_fw_version = json_data["version"]
    new_fw_url = json_data["url"]
    return new_fw_version, new_fw_url


def download_fw_file(download_path, url):
    response = requests.get(url, stream=True)
    response.raise_for_status()  # 当请求失败时抛出异常

    with open(download_path, 'wb') as output_file:
        for chunk in response.iter_content(chunk_size=1024):
            if chunk:  # 如果有数据块，就将其写入文件
                output_file.write(chunk)

    print("更新完成")


if __name__ == '__main__':
    upload_version("Mobius1.5FirmwareRelease_1.0.1.zip", "1.0.1")


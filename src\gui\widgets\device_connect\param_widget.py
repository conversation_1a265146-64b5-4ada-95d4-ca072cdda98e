from PySide6.QtCore import Qt
from PySide6.QtGui import QPixmap
from PySide6.QtWidgets import QSpinBox, QDoubleSpinBox, QComboBox, QLabel, QAbstractSpinBox, QPushButton

from src.gui.widgets.alert_wiidget import AlertWidget
from src.gui.widgets.device_connect.base import ParamWidgetBase
from src.utils.resource_helper import get_image, get_image_path


class NumericParamWidget(ParamWidgetBase):
    def __init__(self, name: str, value_range: tuple, default_value=None, tips=None, parent=None):
        super().__init__(name, parent)

        self._tips = tips

        if isinstance(value_range[0], float) or isinstance(value_range[1], float):
            self.input = QDoubleSpinBox()
            self.input.setDecimals(2)
        else:
            self.input = QSpinBox()

        self.input.setStyleSheet("""
            font-family: 'Alibaba PuHuiTi 2.0';
            font-weight: 300;
            font-size: 20px;
            background-color: transparent;
            color: #FF9429;
            border: none;
        """)
        self.input.setMinimum(value_range[0])
        self.input.setMaximum(value_range[1])

        self.input.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)

        if isinstance(default_value, (int, float)):
            self.input.setValue(default_value)

        self.insert_input_widget(self.input)

        if self._tips:
            self.tip_button = QPushButton(self)
            self.tip_button.clicked.connect(self.on_tip_button_clicked)
            self.tip_button.setCursor(Qt.CursorShape.PointingHandCursor)
            self.tip_button.setIcon(QPixmap(get_image_path("tip.png")).scaled(20, 20, Qt.KeepAspectRatio, Qt.SmoothTransformation))
            self.tip_button.setFixedSize(24, 24)

    def on_tip_button_clicked(self):
        window = AlertWidget(self)
        window.set_image(get_image("alert.svg"))
        window.set_title(self.tr("ID Information"))
        window.set_message(str(self._tips))
        window.set_button_text(self.tr("OK"))
        window.show()

    def resizeEvent(self, event):
        super().resizeEvent(event)
        if self._tips:
            # 把图标移到右边（自定义位置）
            self.tip_button.move(self.width() - 25, (self.height() - 20) // 2)

    def get_value(self):
        return self.input.value()


class SelectParamWidget(ParamWidgetBase):
    def __init__(self, name: str, values: list, default_value=None, parent=None):
        super().__init__(name, parent)
        self.input = QComboBox()
        self.input.addItems([str(i) for i in values])

        self.input.setStyleSheet("""
        QComboBox {
            font-family: 'Alibaba PuHuiTi 2.0';
            font-weight: 300;
            font-size: 20px;
            background-color: transparent;
            color: #FF9429;
            border: none;
        }
        
        QComboBox QAbstractItemView {
            background-color: #232629;
            border: 2px solid #4f5b62;
            border-radius: 4px;
        }
        
        QComboBox::item {
            height: 28px;
            color: #ffffff;
        }
        QComboBox::item:selected {
            color: #000000;
            background-color: #FF9429;
        }
        
        QComboBox::down-arrow {
            image: none;
            width: 0px;
            height: 0px;
        }

        """)

        self.input.setCurrentText(str(default_value))

        self.insert_input_widget(self.input)

        # 添加图标标签
        self.arrow_label = QLabel(self)
        self.arrow_label.setPixmap(get_image("down_arrow.svg").scaled(20, 20, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        self.arrow_label.setFixedSize(20, 20)
        self.arrow_label.setAttribute(Qt.WA_TransparentForMouseEvents)

    def get_value(self):
        return self.input.currentText()

    def get_data(self):
        return self.input.currentData()

    def resizeEvent(self, event):
        super().resizeEvent(event)
        # 把图标移到右边（自定义位置）
        self.arrow_label.move(self.width() - 25, (self.height() - 20) // 2)







from PySide6.QtCore import QRegularExpression, Signal, QTimer
from PySide6.QtGui import QRegularExpressionValidator, QIntValidator, QDoubleValidator
from PySide6.QtWidgets import QFrame, QPushButton, QWidget, QBoxLayout, QLabel, QSpinBox, QDoubleSpinBox, QVBoxLayout, \
    QGroupBox, QLineEdit, QSizePolicy, QHBoxLayout

from ..components.base_widgets import BorderlessLayout
from ..components.capsule_switch import CapsuleSwitch
from ..ui.settings_panel import Ui_Form
from ..ui.settings_param_widget import Ui_Form as ParamUI
from ...utils.resource_helper import get_icon

PARAM_STYLE = """
            font-family: 'Alibaba PuHuiTi 2.0';
            font-weight: {};
            font-size: {};
            background-color: {};
            color: {};
            border: none;
            """


class CustomLabel(QLabel):
    def __init__(self, font_width=400, font_size="16px", font_color="#FFFFFF", bg_color="#293234", parent=None):
        super().__init__(parent)
        self.setStyleSheet(PARAM_STYLE.format(font_width, font_size, bg_color, font_color))


class CustomLineEdit(QLineEdit):
    # 定义信号，当值超出范围时发出
    valueOutOfRange = Signal(str)  # 发送错误信息

    def __init__(self, min_value=None, max_value=None, value_type='int', allow_comma_separated=True, strict_mode=True):
        """
        初始化自定义输入框

        Args:
            min_value: 最小值限制
            max_value: 最大值限制
            value_type: 值类型 ('int', 'float', 'comma_separated')
            allow_comma_separated: 是否允许逗号分隔的多个值
            strict_mode: 严格模式，True时直接阻止输入超出范围的值，False时允许输入但发出警告信号
        """
        super().__init__()
        self.setStyleSheet(PARAM_STYLE.format(400, "20px", "transparent", "white"))
        self.setFixedHeight(40)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # 保存范围限制参数
        self.min_value = min_value
        self.max_value = max_value
        self.value_type = value_type
        self.allow_comma_separated = allow_comma_separated
        self.strict_mode = strict_mode

        # 保存上一次有效的文本
        self._last_valid_text = ""

        # 设置验证器
        self._setup_validator()

        # 连接文本变化信号来验证范围
        if self.strict_mode:
            self.textChanged.connect(self._strict_validate_range)
        else:
            self.textChanged.connect(self._validate_range)

        # 默认设置为只读
        self.setReadOnly(True)

    def _setup_validator(self):
        """根据参数设置相应的验证器"""
        if self.value_type == 'int':
            if self.allow_comma_separated:
                # 允许逗号分隔的整数
                regex = QRegularExpression(r'^\d+(,\d+)*$')
                validator = QRegularExpressionValidator(regex)
            else:
                # 单个整数
                validator = QIntValidator()
                if self.min_value is not None:
                    validator.setBottom(self.min_value)
                if self.max_value is not None:
                    validator.setTop(self.max_value)
        elif self.value_type == 'float':
            if self.allow_comma_separated:
                # 允许逗号分隔的浮点数
                regex = QRegularExpression(r'^\d+(\.\d+)?(,\d+(\.\d+)?)*$')
                validator = QRegularExpressionValidator(regex)
            else:
                # 单个浮点数
                validator = QDoubleValidator()
                if self.min_value is not None:
                    validator.setBottom(self.min_value)
                if self.max_value is not None:
                    validator.setTop(self.max_value)
        else:
            # 默认：逗号分隔的整数
            regex = QRegularExpression(r'^\d+(,\d+)*$')
            validator = QRegularExpressionValidator(regex)

        self.setValidator(validator)

    def _validate_range(self, text):
        """验证输入值是否在指定范围内（非严格模式）"""
        if not text or self.min_value is None and self.max_value is None:
            return

        try:
            if self.allow_comma_separated and ',' in text:
                # 验证逗号分隔的多个值
                values = text.split(',')
                for value_str in values:
                    if value_str.strip():  # 忽略空值
                        value = float(value_str) if self.value_type == 'float' else int(value_str)
                        if not self._is_value_in_range(value):
                            self.valueOutOfRange.emit(f"值 {value} 超出范围 [{self.min_value}, {self.max_value}]")
                            return
            else:
                # 验证单个值
                value = float(text) if self.value_type == 'float' else int(text)
                if not self._is_value_in_range(value):
                    self.valueOutOfRange.emit(f"值 {value} 超出范围 [{self.min_value}, {self.max_value}]")
        except ValueError:
            # 输入格式不正确，由验证器处理
            pass

    def _strict_validate_range(self, text):
        """严格验证输入值范围，直接阻止超出范围的输入"""
        if not text:
            self._last_valid_text = text
            return

        # 如果没有范围限制，直接通过
        if self.min_value is None and self.max_value is None:
            self._last_valid_text = text
            return

        try:
            # 检查输入是否有效
            is_valid = True

            if self.allow_comma_separated and ',' in text:
                # 验证逗号分隔的多个值
                values = text.split(',')
                for value_str in values:
                    if value_str.strip():  # 忽略空值
                        try:
                            value = float(value_str) if self.value_type == 'float' else int(value_str)
                            if not self._is_value_in_range(value):
                                is_valid = False
                                break
                        except ValueError:
                            # 如果当前值无法解析，但可能是用户正在输入过程中，暂时允许
                            # 例如用户输入 "12." 正在输入浮点数
                            if not self._is_partial_input_valid(value_str):
                                is_valid = False
                                break
            else:
                # 验证单个值
                try:
                    value = float(text) if self.value_type == 'float' else int(text)
                    if not self._is_value_in_range(value):
                        is_valid = False
                except ValueError:
                    # 检查是否是部分输入
                    if not self._is_partial_input_valid(text):
                        is_valid = False

            if is_valid:
                self._last_valid_text = text
            else:
                # 阻止输入，恢复到上一次有效的文本
                self.blockSignals(True)  # 阻止信号避免递归
                self.setText(self._last_valid_text)
                self.blockSignals(False)

        except Exception:
            # 发生任何异常时，恢复到上一次有效的文本
            self.blockSignals(True)
            self.setText(self._last_valid_text)
            self.blockSignals(False)

    def _is_partial_input_valid(self, text):
        """检查是否是有效的部分输入（用户正在输入过程中）"""
        if not text:
            return True

        # 允许负号开头（如果最小值允许负数）
        if text == '-' and (self.min_value is None or self.min_value < 0):
            return True

        # 允许小数点开头或结尾（浮点数类型）
        if self.value_type == 'float':
            if text in ['.', '-.'] or text.endswith('.'):
                return True

        # 允许逗号结尾（逗号分隔模式）
        if self.allow_comma_separated and text.endswith(','):
            return True

        return False

    def _is_value_in_range(self, value):
        """检查单个值是否在范围内"""
        if self.min_value is not None and value < self.min_value:
            return False
        if self.max_value is not None and value > self.max_value:
            return False
        return True

    def set_range(self, min_value, max_value):
        """动态设置值范围"""
        self.min_value = min_value
        self.max_value = max_value
        self._setup_validator()
        # 重新验证当前文本
        if self.strict_mode:
            self._strict_validate_range(self.text())
        else:
            self._validate_range(self.text())

    def set_strict_mode(self, strict_mode):
        """动态设置严格模式"""
        if self.strict_mode == strict_mode:
            return

        self.strict_mode = strict_mode

        # 断开旧的信号连接
        self.textChanged.disconnect()

        # 连接新的信号
        if self.strict_mode:
            self.textChanged.connect(self._strict_validate_range)
            self._last_valid_text = self.text()  # 保存当前文本作为有效文本
        else:
            self.textChanged.connect(self._validate_range)


class EditWidget(QFrame):
    confirm_signal = Signal()

    def __init__(self, input_widget=None):
        super().__init__()

        # 关联的输入控件
        self._input_widget = input_widget
        # 保存编辑前的原始值
        self._original_value = ""

        self.edit_widget = QPushButton()
        self.edit_widget.setFixedSize(20, 20)
        self.edit_widget.setIcon(get_icon("edit.svg"))

        self.confirm_button = QPushButton(self.tr("Confirm"))
        self.confirm_button.setStyleSheet(PARAM_STYLE.format(400, "14px", "#3F4749", "white") + "border-radius: 8px;")
        self.confirm_button.setFixedSize(90, 30)

        self.reset_button = QPushButton(self.tr("Reset"))
        self.reset_button.setStyleSheet(PARAM_STYLE.format(400, "14px", "#3F4749", "white") + "border-radius: 8px;")
        self.reset_button.setFixedSize(90, 30)

        self.setLayout(QHBoxLayout())
        self.layout().setContentsMargins(0, 0, 0, 0)

        self.layout().addWidget(self.confirm_button)
        self.layout().addWidget(self.reset_button)
        self.layout().addWidget(self.edit_widget)

        # 默认状态：只显示编辑按钮
        self.confirm_button.hide()
        self.reset_button.hide()
        self.edit_widget.hide()  # TODO 暂时先不开放修改

        # 连接信号
        self.edit_widget.clicked.connect(self._on_edit_clicked)
        self.confirm_button.clicked.connect(self._on_confirm_clicked)
        self.reset_button.clicked.connect(self._on_reset_clicked)

        # 用于跟踪编辑状态
        self._is_editing = False
        # 用于标记是否是用户主动确认（而不是失去焦点）
        self._is_confirming = False

        # 创建定时器用于延迟处理失去焦点事件
        self._focus_lost_timer = QTimer()
        self._focus_lost_timer.setSingleShot(True)
        self._focus_lost_timer.timeout.connect(self._handle_focus_lost)

        # 如果有关联的输入控件，连接其失去焦点信号
        if self._input_widget:
            self.set_input_widget(self._input_widget)

    def set_input_widget(self, input_widget):
        self._input_widget = input_widget
        if hasattr(input_widget, 'editingFinished'):
            input_widget.editingFinished.connect(self.exit_edit_mode_on_focus_lost)

    def _get_input_value(self):
        if not self._input_widget:
            return ""

        if hasattr(self._input_widget, 'text'):
            return self._input_widget.text()
        elif hasattr(self._input_widget, 'value'):
            return str(self._input_widget.value())
        return ""

    def _set_input_value(self, value):
        """设置输入控件的值"""
        if not self._input_widget:
            return

        if hasattr(self._input_widget, 'setText'):
            self._input_widget.setText(str(value))
        elif hasattr(self._input_widget, 'setValue'):
            try:
                # 尝试转换为数值类型
                if isinstance(self._input_widget.value(), int):
                    self._input_widget.setValue(int(value))
                else:
                    self._input_widget.setValue(float(value))
            except (ValueError, TypeError):
                pass

    def _on_edit_clicked(self):
        # 保存当前值作为原始值
        self._original_value = self._get_input_value()

        self._is_editing = True
        self.edit_widget.hide()
        self.confirm_button.show()
        self.reset_button.show()

        # 启用输入控件的编辑功能
        if self._input_widget and hasattr(self._input_widget, 'setReadOnly'):
            self._input_widget.setReadOnly(False)

        # 如果有关联的输入控件，让其获得焦点
        if self._input_widget:
            self._input_widget.setFocus()

    def _on_confirm_clicked(self):
        # 停止失去焦点的定时器
        self._focus_lost_timer.stop()

        # 关键修复：将当前值设置为新的原始值，这样即使失去焦点也不会恢复
        self._original_value = self._get_input_value()

        # 设置确认标志，防止失去焦点时恢复原始值
        self._is_confirming = True
        # 确认更改，不需要恢复原始值
        self._exit_edit_mode()

        self.confirm_signal = Signal()

    def _on_reset_clicked(self):
        # 停止失去焦点的定时器
        self._focus_lost_timer.stop()
        # 恢复到原始值
        self._set_input_value(self._original_value)
        self._exit_edit_mode()

    def _exit_edit_mode(self):
        self._is_editing = False
        self.confirm_button.hide()
        self.reset_button.hide()
        self.edit_widget.show()

        # 禁用输入控件的编辑功能
        if self._input_widget and hasattr(self._input_widget, 'setReadOnly'):
            self._input_widget.setReadOnly(True)

        # 重置确认标志
        self._is_confirming = False

    def exit_edit_mode_on_focus_lost(self):
        # 使用定时器延迟处理，给确认按钮处理留出时间
        if self._is_editing:
            self._focus_lost_timer.start(150)  # 延迟150毫秒

    def _handle_focus_lost(self):
        # 延迟处理失去焦点事件
        # 只有在编辑状态且不是用户主动确认时才恢复原始值
        if self._is_editing and not self._is_confirming:
            self._set_input_value(self._original_value)
            self._exit_edit_mode()

    def is_editing(self):
        return self._is_editing


class SettingParamBase(QFrame, ParamUI):
    def __init__(self, config: dict):
        super().__init__()
        self.setupUi(self)

        self._value_widget = None
        self._config = config
        self.setContentsMargins(15, 0, 15, 15)
        self.setStyleSheet("font-family: 'Alibaba PuHuiTi 2.0'; background-color: #293234; border: none; border-radius: 16px;")
        self._init_ui(config)

    def _init_ui(self, config: dict):
        self.verticalLayout_value.setSpacing(0)

        # 设置主界面长宽
        main_width = config.get("main_width", 480)
        main_height = config.get("main_height", 90)
        self.setFixedSize(main_width, main_height)

        # 是否显示提示标签以及标签文本
        self.label_info.setVisible(config.get("show_prompt", True))
        self.label_info.setText(config.get("prompt_text", ""))
        self.label_info.setStyleSheet(PARAM_STYLE.format(400, "16px", "#293234", "gray"))

    def init_value_widget(self, widget):
        self._value_widget = widget
        self.verticalLayout_value.addWidget(widget)
        if not self._config.get("show_prompt", True):
            widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

    def init_action_widget(self, widget):
        self.verticalLayout_action.addWidget(widget)

    def set_value(self, value):
        if isinstance(self._value_widget, QLabel):
            self._value_widget.setText(str(value))
        elif isinstance(self._value_widget, (QSpinBox, QDoubleSpinBox)):
            self._value_widget.setValue(value)


class SettingsPanelWidget(QFrame, Ui_Form):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUi(self)

        self._init_ui()

    def _init_ui(self):
        self.setStyleSheet("border: none;")

        # Device Info
        self.groupBox_device_info.setLayout(QVBoxLayout())
        self.groupBox_device_info.layout().setContentsMargins(0, 30, 0, 0)
        self.groupBox_device_info.setStyleSheet("QGroupBox::title { font-size: 16px; color: gray}")
        self.sku_widget = SettingParamBase({"prompt_text": "SKU"})
        self.sku_widget.init_value_widget(CustomLabel())
        self.sn_widget = SettingParamBase({"prompt_text": "SN"})
        self.sn_widget.init_value_widget(CustomLabel())
        self.fw_version_widget = SettingParamBase({"prompt_text": "Firmware Version"})
        self.fw_version_widget.init_value_widget(CustomLabel())
        self.upgrade_fw_widget = QPushButton(self.tr("OTA"))
        self.upgrade_fw_widget.setFixedSize(200, 58)
        self.upgrade_fw_widget.setStyleSheet(PARAM_STYLE.format(400, "20px", "#3E4647", "white"))
        self.fw_version_widget.init_action_widget(self.upgrade_fw_widget)
        self.id_widget = SettingParamBase({"prompt_text": "ID"})
        self.id_widget.init_value_widget(CustomLabel())
        self.baudrate_widget = SettingParamBase({"prompt_text": "Baudrate"})
        self.baudrate_widget.init_value_widget(CustomLabel())
        self.protocol_name_widget = SettingParamBase({"prompt_text": "Protocol"})
        self.protocol_name_widget.init_value_widget(CustomLabel())
        self.auto_zeroing_widget = SettingParamBase({"prompt_text": "Auto-zeroing"})
        self.auto_zeroing_widget.label_info.setStyleSheet(PARAM_STYLE.format(400, "20px", "#293234", "#FFFFFF"))
        self.auto_zeroing_widget.init_value_widget(CustomLabel(font_size="12px", font_color="gray"))
        self.auto_zeroing_widget._value_widget.setFixedHeight(35)
        self.auto_zeroing_widget.set_value("This button activates automatic hand zeroing upon each restart\nrestoring the default open-hand posture.")
        self.enable_auto_zeroing_widget = CapsuleSwitch()
        self.enable_auto_zeroing_widget.setFixedSize(48, 28)
        self.auto_zeroing_widget.init_action_widget(self.enable_auto_zeroing_widget)

        self.groupBox_device_info.layout().addWidget(self.sku_widget)
        self.groupBox_device_info.layout().addWidget(self.sn_widget)
        self.groupBox_device_info.layout().addWidget(self.fw_version_widget)
        self.groupBox_device_info.layout().addWidget(self.id_widget)
        self.groupBox_device_info.layout().addWidget(self.baudrate_widget)
        self.groupBox_device_info.layout().addWidget(self.protocol_name_widget)
        self.groupBox_device_info.layout().addWidget(self.auto_zeroing_widget)

        # Device Name
        self.groupBox_device_name.setLayout(QVBoxLayout())
        self.groupBox_device_name.layout().setContentsMargins(0, 30, 0, 0)
        self.groupBox_device_name.setStyleSheet("QGroupBox::title { font-size: 16px; color: gray}")
        self.device_name_widget = SettingParamBase({"show_prompt": False})
        self.device_name_lineedit = QLineEdit()
        self.device_name_lineedit.setText("")
        self.device_name_lineedit.setMaxLength(18)
        self.device_name_lineedit.setFixedHeight(60)
        self.device_name_lineedit.setClearButtonEnabled(True)
        self.device_name_lineedit.setStyleSheet("""
            font-size: 28px; 
            color: white;
            QLineEdit::clear-button {
                background-color: red;  /* 清除按钮的背景色 */
                width: 10px;
                height: 10px;
            }
            QLineEdit::clear-button:hover {
                background-color: red;  /* 悬停时的背景色 */
            }
        """)
        self.device_name_widget.init_value_widget(self.device_name_lineedit)
        self.groupBox_device_name.layout().addWidget(self.device_name_widget)

        # Notification
        self.groupBox_notification.setLayout(QVBoxLayout())
        self.groupBox_notification.layout().setContentsMargins(0, 30, 0, 0)
        self.groupBox_notification.setStyleSheet("QGroupBox::title { font-size: 16px; color: gray}")
        self.turbo_widget = SettingParamBase({"show_prompt": False, "main_width": 320, "main_height": 64})
        self.turbo_widget.init_value_widget(CustomLabel(font_size="20px", font_color="white"))
        self.turbo_widget._value_widget.setFixedHeight(40)
        self.turbo_widget.set_value("Turbo")
        self.enabl_turbo_widget = CapsuleSwitch()
        self.enabl_turbo_widget.setFixedSize(48, 28)
        self.turbo_widget.init_action_widget(self.enabl_turbo_widget)
        self.led_widget = SettingParamBase({"show_prompt": False, "main_width": 320, "main_height": 64})
        self.led_widget.init_value_widget(CustomLabel(font_size="20px", font_color="white"))
        self.led_widget._value_widget.setFixedHeight(40)
        self.led_widget.set_value("Light")
        self.enabl_led_widget = CapsuleSwitch()
        self.enabl_led_widget.setFixedSize(48, 28)
        self.led_widget.init_action_widget(self.enabl_led_widget)
        self.buzzer_widget = SettingParamBase({"show_prompt": False, "main_width": 320, "main_height": 64})
        self.buzzer_widget.init_value_widget(CustomLabel(font_size="20px", font_color="white"))
        self.buzzer_widget._value_widget.setFixedHeight(40)
        self.buzzer_widget.set_value("Buzzer")
        self.enabl_buzzer_widget = CapsuleSwitch()
        self.enabl_buzzer_widget.setFixedSize(48, 28)
        self.buzzer_widget.init_action_widget(self.enabl_buzzer_widget)
        self.vibration_widget = SettingParamBase({"show_prompt": False, "main_width": 320, "main_height": 64})
        self.vibration_widget.init_value_widget(CustomLabel(font_size="20px", font_color="white"))
        self.vibration_widget._value_widget.setFixedHeight(40)
        self.vibration_widget.set_value("Vibration")
        self.enabl_vibration_widget = CapsuleSwitch()
        self.enabl_vibration_widget.setFixedSize(48, 28)
        self.vibration_widget.init_action_widget(self.enabl_vibration_widget)
        self.groupBox_notification.layout().addWidget(self.turbo_widget)
        self.groupBox_notification.layout().addWidget(self.led_widget)
        self.groupBox_notification.layout().addWidget(self.buzzer_widget)
        self.groupBox_notification.layout().addWidget(self.vibration_widget)

        #  restore factory Defaults
        self.pushButton_restore_factory.setFixedSize(320, 60)
        self.pushButton_restore_factory.setStyleSheet("font-family: 'Alibaba PuHuiTi 2.0'; background-color: #293234; border: none; border-radius: 16px; font-size: 20px; border-radius: 8px; color: white;")

        # 手指范围限制
        self.groupBox_limit_range.setLayout(QVBoxLayout())
        self.groupBox_limit_range.layout().setContentsMargins(0, 30, 0, 0)
        self.groupBox_limit_range.setStyleSheet("QGroupBox::title { font-size: 16px; color: gray}")

        # min_position (假设位置范围 0-4095，允许逗号分隔的多个值，严格模式)
        self.min_position_widget = SettingParamBase({"prompt_text": "min_position", "main_width": 688})
        self.min_position_input_widget = CustomLineEdit(min_value=0, max_value=4095, value_type='int', allow_comma_separated=True, strict_mode=True)
        self.min_position_widget.init_value_widget(self.min_position_input_widget)
        self.min_position_edit_widget = EditWidget(self.min_position_input_widget)
        self.min_position_widget.init_action_widget(self.min_position_edit_widget)

        # max_position (假设位置范围 0-4095，允许逗号分隔的多个值，严格模式)
        self.max_position_widget = SettingParamBase({"prompt_text": "max_position", "main_width": 688})
        self.max_position_input_widget = CustomLineEdit(min_value=0, max_value=4095, value_type='int', allow_comma_separated=True, strict_mode=True)
        self.max_position_widget.init_value_widget(self.max_position_input_widget)
        self.max_position_edit_widget = EditWidget(self.max_position_input_widget)
        self.max_position_widget.init_action_widget(self.max_position_edit_widget)

        # max_speed (假设速度范围 1-1000，允许逗号分隔的多个值，严格模式)
        self.max_speed_widget = SettingParamBase({"prompt_text": "max_speed", "main_width": 688})
        self.max_speed_input_widget = CustomLineEdit(min_value=1, max_value=1000, value_type='int', allow_comma_separated=True, strict_mode=True)
        self.max_speed_widget.init_value_widget(self.max_speed_input_widget)
        self.max_speed_edit_widget = EditWidget(self.max_speed_input_widget)
        self.max_speed_widget.init_action_widget(self.max_speed_edit_widget)

        # max_force (假设力度范围 1-1000，允许逗号分隔的多个值，严格模式)
        self.max_force_widget = SettingParamBase({"prompt_text": "max_force", "main_width": 688})
        self.max_force_input_widget = CustomLineEdit(min_value=1, max_value=1000, value_type='int', allow_comma_separated=True, strict_mode=True)
        self.max_force_widget.init_value_widget(self.max_force_input_widget)
        self.max_force_edit_widget = EditWidget(self.max_force_input_widget)
        self.max_force_widget.init_action_widget(self.max_force_edit_widget)

        # 连接范围验证信号到错误处理
        self.min_position_input_widget.valueOutOfRange.connect(self._show_range_error)
        self.max_position_input_widget.valueOutOfRange.connect(self._show_range_error)
        self.max_speed_input_widget.valueOutOfRange.connect(self._show_range_error)
        self.max_force_input_widget.valueOutOfRange.connect(self._show_range_error)

        self.groupBox_limit_range.layout().addWidget(self.min_position_widget)
        self.groupBox_limit_range.layout().addWidget(self.max_position_widget)
        self.groupBox_limit_range.layout().addWidget(self.max_speed_widget)
        self.groupBox_limit_range.layout().addWidget(self.max_force_widget)

    def _show_range_error(self, error_message):
        """显示范围错误信息"""
        print(f"输入范围错误: {error_message}")
        # 这里可以添加更复杂的错误显示逻辑，比如弹出提示框或状态栏显示
        # 例如：QMessageBox.warning(self, "输入错误", error_message)

    def _set_sku(self, sku_str):
        self.sku_widget.set_value(sku_str)

    def _set_sn(self, sn_str):
        self.sn_widget.set_value(sn_str)

    def _set_fw_version(self, fw_version):
        self.fw_version_widget.set_value(fw_version)

    def _set_id(self, _id):
        self.id_widget.set_value(_id)

    def _set_baudrate(self, _id):
        self.baudrate_widget.set_value(_id)
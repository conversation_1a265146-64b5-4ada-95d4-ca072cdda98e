import numpy as np
from PySide6.QtCore import QAbstractTableModel, QModelIndex, QAbstractItemModel, Qt
from PySide6.QtWidgets import QWidget, QFrame, QVBoxLayout, QTableView, QHeaderView, QLabel, QHBoxLayout
from ..ui.data_display import Ui_Form
import pyqtgraph as pg

from ...core.hand_specifications import HandType

_TABLE_H_TITLE = ["Thumb(Ad/Ab)", "Thumb", "Index", "Middle", "Ring", "Picky"]
_TABLE_V_TITLE = ["Position(°)", "Speed(°/s)", "Force(mA)"]


class RelativeTimeAxisItem(pg.AxisItem):

    def __init__(self, *args, **kwargs):
        super(RelativeTimeAxisItem, self).__init__(*args, **kwargs)
        self.setLabel('Time (s)')

    def tickStrings(self, values, scale, spacing):
        strings = []
        for value in values:
            strings.append(f"{value:.2f}s")
        return strings


class FingerDataPlot(pg.PlotWidget):
    def __init__(self, parent=None, sample_rate=250, display_seconds=4, min_range_seconds=1):
        super(FingerDataPlot, self).__init__(parent)

        self.sample_rate = sample_rate  # samples per second
        self.display_seconds = display_seconds  # number of seconds to display
        self.min_range_seconds = min_range_seconds  # minimum range in seconds

        # # 布局
        # self.layout = QVBoxLayout(self)
        # self.layout.setContentsMargins(0, 0, 0, 0)
        # self.setLayout(self.layout)
        #
        # self.axis = RelativeTimeAxisItem(orientation='bottom')
        # self.plot_widget = pg.PlotWidget(axisItems={'bottom': self.axis})
        self.setBackground("#2B3234")
        # self.layout.addWidget(self.plot_widget)
        #
        # # 绘图启用鼠标交互
        # self.plot_widget.setMouseEnabled(x=True, y=True)
        # self.plot_widget.setInteractive(True)
        #
        # # 在 y 轴上启用自动量程，但不在 x 轴上启用自动量程
        # self.plot_widget.setAutoVisible(y=True)

        # 添加图例
        self.addLegend()

        self.curves = []
        self.data_index = 0  # Current data index (increments with each new data point)
        self.start_time = 0  # Track the start time for relative timing

        # 设置绘图外观
        self.showGrid(x=True, y=True, alpha=0.3)
        self.setLabel('left', 'Value')

        # # 使用鼠标滚轮启用缩放
        # self.plot_widget.setMouseEnabled(x=True, y=True)
        #
        # # 启用拖动以修改 x 轴范围
        # self.plot_widget.setMenuEnabled(False)  # 禁用右键菜单
        #
        # # 设置 ViewBox 以进行拖动
        # self.view_box = self.plot_widget.getPlotItem().getViewBox()
        # self.view_box.setMouseMode(self.view_box.RectMode)
        #
        # # 连接信号以更改范围
        # self.view_box.sigXRangeChanged.connect(self.on_x_range_changed)
        #
        # # 初始化 x 轴范围以显示最新数据
        # self.set_default_range()

        self.add_curve("Thumb(Fl/Ex)", color="#78BB07")
        self.add_curve("Thumb(Ad/Ab)", color="#4454C6")
        self.add_curve("Index", color="#F0A501")
        self.add_curve("Middle", color="#E63D45")
        self.add_curve("Ring", color="#5098BD")
        self.add_curve("Pinky", color="#008545")

    # def set_default_range(self):
    #     self.plot_widget.setXRange(0, self.display_seconds)
    #
    # def on_x_range_changed(self, view_box, range_values):
    #     min_x, max_x = range_values
    #
    #     if max_x - min_x < self.min_range_seconds:
    #         center = (max_x + min_x) / 2
    #         half_range = self.min_range_seconds / 2
    #         self.plot_widget.setXRange(center - half_range, center + half_range, padding=0)

    def add_curve(self, name, color):
        pen = pg.mkPen(color=color, width=2) if color else None
        curve = self.plot(name=name, pen=pen)
        self.curves.append(curve)
        return curve

    # def update_curve(self, name, y_value):
    #     if name not in self.curves:
    #         self.add_curve(name)
    #
    #     current_time = self.data_index / self.sample_rate
    #
    #     self.curves[name]['x_data'].append(current_time)
    #     self.curves[name]['y_data'].append(y_value)
    #
    #     current_window_start = current_time - self.display_seconds * 1.5  # Keep some margin
    #     while len(self.curves[name]['x_data']) > 1 and self.curves[name]['x_data'][0] < current_window_start:
    #         self.curves[name]['x_data'].pop(0)
    #         self.curves[name]['y_data'].pop(0)
    #
    #     self.curves[name]['curve'].setData(
    #         self.curves[name]['x_data'],
    #         self.curves[name]['y_data']
    #     )
    #
    #     self.data_index += 1
    #
    #     self.update_view()

    def update_curve_data(self, y_datas):
        x_data = np.arange(len(y_datas[0]))
        for i in range(6):
            self.curves[i].setData(x_data, y_datas[i])

        # self.update_view()

    # def update_view(self):
    #     if any(len(curve['x_data']) > 0 for curve in self.curves.values()):
    #         max_x = max(max(curve['x_data']) if curve['x_data'] else 0
    #                     for curve in self.curves.values())
    #
    #         # Set the range to show the most recent data
    #         self.plot_widget.setXRange(max(0, max_x - self.display_seconds), max_x)
    #
    # def clear_curve(self, name):
    #     if name in self.curves:
    #         self.curves[name]['x_data'] = []
    #         self.curves[name]['y_data'] = []
    #         self.curves[name]['curve'].setData([], [])
    #
    # def clear_all(self):
    #     for name in self.curves:
    #         self.clear_curve(name)
    #     self.data_index = 0
    #     self.start_time = 0
    #     self.set_default_range()
    #
    # def set_time_range(self, start_seconds, end_seconds):
    #     self.plot_widget.setXRange(start_seconds, end_seconds)
    #
    # def set_value_range(self, min_value, max_value):
    #     self.plot_widget.setYRange(min_value, max_value)
    #
    # def auto_range(self):
    #     self.plot_widget.enableAutoRange()
    #
    # def set_display_seconds(self, seconds):
    #     self.display_seconds = max(seconds, self.min_range_seconds)
    #     self.update_view()
    #
    # def set_min_range_seconds(self, seconds):
    #     self.min_range_seconds = max(seconds, 0.1)  # Ensure it's not too small

    def set_sample_rate(self, rate):
        self.sample_rate = rate


class MotorStatusTableViewModel(QAbstractTableModel):
    def __init__(self, parent=None):
        super(MotorStatusTableViewModel, self).__init__(parent)
        self._data_list = []
        self.mode = 0

    @property
    def data_list(self):
        return self._data_list

    @data_list.setter
    def data_list(self, data_list):
        self._data_list = data_list
        self.updateModel()

    def rowCount(self, parent: QModelIndex = ...):
        return 3

    def columnCount(self, parent: QModelIndex = ...):
        return 6

    def flags(self, index):
        if not index.isValid():
            return QAbstractItemModel.flags(self, index)
        flag = QAbstractTableModel.flags(self, index)
        flag |= Qt.ItemIsEnabled
        return flag

    def data(self, index: QModelIndex, role: int = ...):
        if not index.isValid():
            return None
        if role == Qt.TextAlignmentRole:
            value = Qt.AlignCenter
            return value
        if role == Qt.DisplayRole:
            if len(self._data_list):
                if len(self._data_list[index.row()]):
                    return self._data_list[index.row()][index.column()]
            return None
        else:
            return None

    def headerData(self, section: int, orientation: Qt.Orientation, role: int = ...):
        if orientation == Qt.Horizontal and role == Qt.DisplayRole:
            return _TABLE_H_TITLE[section]
        elif orientation == Qt.Vertical and role == Qt.DisplayRole:
            text = _TABLE_V_TITLE[section]
            if self.mode == 0:
                return text.split("(")[0]
            else:
                return text
        return QAbstractTableModel.headerData(self, section, orientation, role)

    def updateModel(self):
        self.dataChanged.emit(self.createIndex(0, 0), self.createIndex(self.rowCount() - 1, self.columnCount() - 1))
        self.layoutAboutToBeChanged.emit()
        self.layoutChanged.emit()


class FingerDataTableView(QTableView):
    def __init__(self, parent=None):
        super(FingerDataTableView, self).__init__(parent)

        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table_model = MotorStatusTableViewModel(parent=self)
        self.setModel(self.table_model)
        self.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.verticalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        # self.setMinimumWidth(500)
        # self.setMinimumHeight(170)

        # Enhanced styling
        self.setStyleSheet("""
            QTableView {
                background-color: #2B3234; 
                color: white;
                font-size: 18pt;
                border: none;
                gridline-color: #3C4144;
                alternate-background-color: #31373A;
            }
            QTableView::item {
                padding: 5px;
                border-bottom: 1px solid #3C4144;
                color: #FFFFFF;
                font-weight: 500;
            }
            QTableView::item:hover {
                background-color: #3D474C;
            }
            QTableView::item:selected {
                background-color: #445055;
                color: #FF9429;
            }
            QHeaderView::section {
                background-color: #343A3D;
                color: #FF9429;
                font-size: 18pt;
                font-weight: bold;
                padding: 5px;
                border: none;
                border-bottom: 2px solid #FF9429;
                text-align: center;
            }
            QHeaderView::section:horizontal {
                padding-top: 8px;
                padding-bottom: 8px;
            }
            QHeaderView::section:vertical {
                padding-left: 8px;
                padding-right: 8px;
            }
        """)

        # Ensure text is centered in cells
        self.setAlternatingRowColors(True)
        
        # Set row height to accommodate larger text
        verticalHeader = self.verticalHeader()
        verticalHeader.setDefaultSectionSize(50)
        
        # Center text in all cells
        self.horizontalHeader().setDefaultAlignment(Qt.AlignCenter)
        self.verticalHeader().setDefaultAlignment(Qt.AlignCenter)


class FingerDataDisplayWidget(QFrame, Ui_Form):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUi(self)

        self._display_type = "graph"

        # 绘图
        self.finger_data_plot = FingerDataPlot()
        self.finger_data_plot_extra = FingerDataPlot()

        # 表格
        self.finger_data_table = FingerDataTableView()
        self.finger_data_table_extra = FingerDataTableView()

        self.set_extra_widget_visible(False)

        self._init_ui()

        self._connect_event()

    def _init_ui(self):
        self.setStyleSheet("background-color: #22262E;")

        self.tabWidget.tabBar().hide()
        self.tabWidget.setStyleSheet("border: none;")
        self.tabWidget.setContentsMargins(0, 0, 0, 0)

        plot_widget = QWidget()
        plot_widget.setLayout(QHBoxLayout())
        plot_widget.layout().setContentsMargins(0, 0, 0, 0)
        plot_widget.layout().addWidget(self.finger_data_plot)
        plot_widget.layout().addWidget(self.finger_data_plot_extra)
        self.tabWidget.addTab(plot_widget, "")

        table_widget = QWidget()
        table_widget.setLayout(QHBoxLayout())
        table_widget.layout().setContentsMargins(0, 0, 0, 0)
        table_widget.layout().addWidget(self.finger_data_table)
        table_widget.layout().addWidget(self.finger_data_table_extra)
        self.tabWidget.addTab(table_widget, "")

        self.tabWidget.setCurrentIndex(0)

        for button in [self.pushButton_chart, self.pushButton_number, self.pushButton_position, self.pushButton_speed, self.pushButton_force]:
            button.setCheckable(True)
            button.setFixedSize(108, 48)
            button.setStyleSheet("""
            QPushButton {
                background-color: #2E3134;
                font-size: 20px;
                color: white;
                border: none;
                padding: 0px;
                border-radius: 5px;
            }
            QPushButton:checked {
                border: 1px solid white;
                background-color: #3C3F42;
            }
        """)

        self.radioButton_percentage.setChecked(True)
        self.pushButton_chart.setChecked(True)
        self.pushButton_position.setChecked(True)

        self.pushButton_chart.setText(self.tr("Chart View"))
        self.pushButton_number.setText(self.tr("Table View "))

    def _connect_event(self):
        self.pushButton_chart.clicked.connect(lambda: self.change_display_type("graph"))
        self.pushButton_number.clicked.connect(lambda: self.change_display_type("table"))

        for button in [self.pushButton_position, self.pushButton_speed, self.pushButton_force]:
            button.clicked.connect(self._on_change_display_data_type_button)

    def set_extra_widget_visible(self, visible: bool):
        self.finger_data_plot_extra.setVisible(visible)
        self.finger_data_table_extra.setVisible(visible)

    def change_display_type(self, display_type: str):
        if display_type == "graph":
            self.tabWidget.setCurrentIndex(0)
        else:
            self.tabWidget.setCurrentIndex(1)

        self.pushButton_number.setChecked(not display_type=="graph")
        self.pushButton_chart.setChecked(display_type=="graph")

        for button in [self.pushButton_position, self.pushButton_speed, self.pushButton_force]:
            button.setVisible(display_type=="graph")

    def _on_change_display_data_type_button(self):
        if self.sender() == self.pushButton_position:
            self.pushButton_position.setChecked(True)
            self.pushButton_speed.setChecked(False)
            self.pushButton_force.setChecked(False)
        elif self.sender() == self.pushButton_speed:
            self.pushButton_speed.setChecked(True)
            self.pushButton_position.setChecked(False)
            self.pushButton_force.setChecked(False)
        else:
            self.pushButton_force.setChecked(True)
            self.pushButton_position.setChecked(False)
            self.pushButton_speed.setChecked(False)

    def update_plot(self, hand_types, buffer):
        def update_hand_data(hand_type, plot_widget, table_widget=None):
            hand_buffer = buffer[hand_type]

            # 更新表格（如果需要）
            if hand_buffer['status'] is not None and table_widget is not None:
                for sublist in  hand_buffer['status']:
                    sublist[0], sublist[1] = sublist[1], sublist[0]  # 交换第一个和第二个元素， 因为UI表格显示拇指尖和拇指根的位置是反过来的
                table_widget.table_model.data_list = hand_buffer['status']

            # 选择数据类型并更新图表
            if self.pushButton_position.isChecked():
                data = hand_buffer['position']
            elif self.pushButton_speed.isChecked():
                data = hand_buffer['speed']
            else:
                data = hand_buffer['current']
            plot_widget.update_curve_data(data)

        if len(hand_types) == 1:
            hand_type = hand_types[0]
            update_hand_data(hand_type, self.finger_data_plot, self.finger_data_table)
        else:
            # 默认将左手设为主表和主图，右手为副图
            update_hand_data(HandType.Left, self.finger_data_plot, self.finger_data_table)
            update_hand_data(HandType.Right, self.finger_data_plot_extra, self.finger_data_table_extra)




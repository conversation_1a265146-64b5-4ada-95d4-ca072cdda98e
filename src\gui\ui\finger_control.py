# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'finger_control.ui'
##
## Created by: Qt User Interface Compiler version 6.4.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (<PERSON><PERSON><PERSON>, Q<PERSON><PERSON>r, Q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>urs<PERSON>,
    <PERSON><PERSON><PERSON>, Q<PERSON>ontData<PERSON>, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QAbstractSpinBox, QApplication, QDoubleSpinBox, QFrame,
    QHBoxLayout, QLabel, QSizePolicy, QSpacerItem,
    QTextEdit, QToolButton, QVBoxLayout, QWidget)

from ..components.custom_slider import (FingerSliderH, FingerSliderV)

class Ui_Form(object):
    def setupUi(self, Form):
        if not Form.objectName():
            Form.setObjectName(u"Form")
        Form.resize(273, 411)
        sizePolicy = QSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(Form.sizePolicy().hasHeightForWidth())
        Form.setSizePolicy(sizePolicy)
        self.verticalLayout_3 = QVBoxLayout(Form)
        self.verticalLayout_3.setSpacing(2)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.verticalLayout_3.setContentsMargins(15, 15, 15, 15)
        self.horizontalLayout_2 = QHBoxLayout()
        self.horizontalLayout_2.setSpacing(2)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.toolButton_dot = QToolButton(Form)
        self.toolButton_dot.setObjectName(u"toolButton_dot")
        self.toolButton_dot.setMinimumSize(QSize(15, 15))
        self.toolButton_dot.setMaximumSize(QSize(15, 15))
        self.toolButton_dot.setStyleSheet(u"border: 1px solid black; border-radius: 7px;")

        self.horizontalLayout_2.addWidget(self.toolButton_dot)

        self.textEdit_name = QTextEdit(Form)
        self.textEdit_name.setObjectName(u"textEdit_name")
        sizePolicy.setHeightForWidth(self.textEdit_name.sizePolicy().hasHeightForWidth())
        self.textEdit_name.setSizePolicy(sizePolicy)
        self.textEdit_name.setMaximumSize(QSize(16777215, 39))
        self.textEdit_name.setSizeIncrement(QSize(0, 39))

        self.horizontalLayout_2.addWidget(self.textEdit_name)


        self.verticalLayout_3.addLayout(self.horizontalLayout_2)

        self.horizontalLayout_3 = QHBoxLayout()
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.verticalLayout_2 = QVBoxLayout()
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.verticalLayout = QVBoxLayout()
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.label = QLabel(Form)
        self.label.setObjectName(u"label")
        self.label.setStyleSheet(u"color: gray;font-size: 16px;")

        self.verticalLayout.addWidget(self.label)

        self.doubleSpinBox_speed = QDoubleSpinBox(Form)
        self.doubleSpinBox_speed.setObjectName(u"doubleSpinBox_speed")
        sizePolicy1 = QSizePolicy(QSizePolicy.Minimum, QSizePolicy.Expanding)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.doubleSpinBox_speed.sizePolicy().hasHeightForWidth())
        self.doubleSpinBox_speed.setSizePolicy(sizePolicy1)
        self.doubleSpinBox_speed.setStyleSheet(u"border-radius: 5px")
        self.doubleSpinBox_speed.setButtonSymbols(QAbstractSpinBox.UpDownArrows)
        self.doubleSpinBox_speed.setDecimals(1)

        self.verticalLayout.addWidget(self.doubleSpinBox_speed)

        self.verticalSpacer_2 = QSpacerItem(20, 15, QSizePolicy.Minimum, QSizePolicy.Fixed)

        self.verticalLayout.addItem(self.verticalSpacer_2)

        self.label_2 = QLabel(Form)
        self.label_2.setObjectName(u"label_2")
        self.label_2.setStyleSheet(u"color: gray;font-size: 16px;")

        self.verticalLayout.addWidget(self.label_2)

        self.doubleSpinBox_force = QDoubleSpinBox(Form)
        self.doubleSpinBox_force.setObjectName(u"doubleSpinBox_force")
        sizePolicy1.setHeightForWidth(self.doubleSpinBox_force.sizePolicy().hasHeightForWidth())
        self.doubleSpinBox_force.setSizePolicy(sizePolicy1)
        self.doubleSpinBox_force.setStyleSheet(u"border-radius: 2px")
        self.doubleSpinBox_force.setButtonSymbols(QAbstractSpinBox.UpDownArrows)
        self.doubleSpinBox_force.setDecimals(1)

        self.verticalLayout.addWidget(self.doubleSpinBox_force)

        self.verticalSpacer = QSpacerItem(20, 15, QSizePolicy.Minimum, QSizePolicy.Fixed)

        self.verticalLayout.addItem(self.verticalSpacer)

        self.label_3 = QLabel(Form)
        self.label_3.setObjectName(u"label_3")
        self.label_3.setStyleSheet(u"color: gray;font-size: 16px;")

        self.verticalLayout.addWidget(self.label_3)

        self.doubleSpinBox_position = QDoubleSpinBox(Form)
        self.doubleSpinBox_position.setObjectName(u"doubleSpinBox_position")
        sizePolicy1.setHeightForWidth(self.doubleSpinBox_position.sizePolicy().hasHeightForWidth())
        self.doubleSpinBox_position.setSizePolicy(sizePolicy1)
        self.doubleSpinBox_position.setStyleSheet(u"border-radius: 2px")
        self.doubleSpinBox_position.setButtonSymbols(QAbstractSpinBox.UpDownArrows)
        self.doubleSpinBox_position.setDecimals(1)

        self.verticalLayout.addWidget(self.doubleSpinBox_position)


        self.verticalLayout_2.addLayout(self.verticalLayout)

        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.frame_slider_bottom = FingerSliderH(Form)
        self.frame_slider_bottom.setObjectName(u"frame_slider_bottom")
        self.frame_slider_bottom.setFrameShape(QFrame.NoFrame)

        self.horizontalLayout.addWidget(self.frame_slider_bottom)

        self.frame_zw = QFrame(Form)
        self.frame_zw.setObjectName(u"frame_zw")
        self.frame_zw.setFrameShape(QFrame.NoFrame)

        self.horizontalLayout.addWidget(self.frame_zw)

        self.horizontalLayout.setStretch(0, 1)

        self.verticalLayout_2.addLayout(self.horizontalLayout)


        self.horizontalLayout_3.addLayout(self.verticalLayout_2)

        self.frame_slider_right = FingerSliderV(Form)
        self.frame_slider_right.setObjectName(u"frame_slider_right")
        self.frame_slider_right.setFrameShape(QFrame.NoFrame)

        self.horizontalLayout_3.addWidget(self.frame_slider_right)

        self.horizontalLayout_3.setStretch(0, 1)

        self.verticalLayout_3.addLayout(self.horizontalLayout_3)


        self.retranslateUi(Form)

        QMetaObject.connectSlotsByName(Form)
    # setupUi

    def retranslateUi(self, Form):
        Form.setWindowTitle(QCoreApplication.translate("Form", u"Form", None))
        self.toolButton_dot.setText("")
        self.textEdit_name.setHtml(QCoreApplication.translate("Form", u"<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"hr { height: 1px; border-width: 0; }\n"
"li.unchecked::marker { content: \"\\2610\"; }\n"
"li.checked::marker { content: \"\\2612\"; }\n"
"</style></head><body style=\" font-family:'Microsoft YaHei UI'; font-size:9pt; font-weight:400; font-style:normal;\">\n"
"<p style=\"-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><br /></p></body></html>", None))
        self.label.setText(QCoreApplication.translate("Form", u"Speed", None))
        self.label_2.setText(QCoreApplication.translate("Form", u"Force", None))
        self.label_3.setText(QCoreApplication.translate("Form", u"Position", None))
    # retranslateUi


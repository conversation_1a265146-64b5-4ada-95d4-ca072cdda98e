from PySide6.QtCore import Qt
from PySide6.QtWidgets import QFrame, QHBoxLayout, QVBoxLayout, QBoxLayout


# 无边框的窗口基类
class BorderlessWidgetBase(QFrame):
    def __init__(self, parent=None):
        super().__init__(parent)

        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)


# 无边距的布局
class BorderlessLayout(QBoxLayout):
    def __init__(self, direction: QBoxLayout.Direction, parent=None):
        super().__init__(direction, parent)
        self.setContentsMargins(0, 0, 0, 0)


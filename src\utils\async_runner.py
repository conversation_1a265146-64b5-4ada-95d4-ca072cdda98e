import asyncio
import threading
import time
from typing import Callable, Any, Optional
from loguru import logger

class AsyncRunner:
    """异步任务运行器，在独立线程中运行事件循环"""
    _instance = None
    
    @classmethod
    def instance(cls):
        if cls._instance is None:
            cls._instance = AsyncRunner()
        return cls._instance
    
    def __init__(self):
        self._loop_thread = None
        self._event_loop = None
        self._setup_event_loop()
    
    def _setup_event_loop(self):
        """设置事件循环并在专用线程中运行"""
        if self._loop_thread is not None:
            return
            
        def run_event_loop():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            self._event_loop = loop
            loop.run_forever()
            
        self._loop_thread = threading.Thread(target=run_event_loop, daemon=True)
        self._loop_thread.start()
        
        # 等待事件循环准备好
        while self._event_loop is None:
            pass
    
    def run_async(self, coro, on_success=None, on_error=None, asyncio_sleep=None):
        """
        运行异步协程并处理回调
        
        Args:
            coro: 异步协程
            on_success: 成功回调
            on_error: 错误回调
            asyncio_sleep: 函数调用结束后等待回调时长
        """
        async def wrapped_coro():
            try:
                result = await coro
                if asyncio_sleep:
                    await asyncio.sleep(asyncio_sleep)
                if on_success:
                    # 确保回调在主线程执行
                    self._event_loop.call_soon_threadsafe(
                        lambda: on_success(result)
                    )
            except Exception as e:
                import traceback
                traceback.print_exc()
                if on_error:
                    self._event_loop.call_soon_threadsafe(
                        lambda e=e: on_error(str(e))
                    )
        
        asyncio.run_coroutine_threadsafe(wrapped_coro(), self._event_loop)

# 方便使用的函数
def run_function(func, on_success=None, on_error=None, *args, **kwargs):
    """
    使用单例AsyncRunner运行函数（自动检测同步或异步）
    
    Args:
        func: 要运行的函数（同步或异步）
        on_success: 操作成功完成时的回调函数
        on_error: 操作失败时的回调函数
        *args, **kwargs: 传递给函数的参数
    """
    # 检查是否为异步函数
    if asyncio.iscoroutinefunction(func):
        # 异步函数处理
        async def wrapped_coro():
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                logger.error(f"异步函数执行失败: {str(e)}")
                raise
        
        AsyncRunner.instance().run_async(wrapped_coro(), on_success, on_error)
    else:
        # 同步函数处理 - 直接在当前线程中执行
        try:
            result = func(*args, **kwargs)
            if on_success:
                on_success(result)
        except Exception as e:
            logger.error(f"同步函数执行失败: {str(e)}")
            if on_error:
                on_error(str(e)) 
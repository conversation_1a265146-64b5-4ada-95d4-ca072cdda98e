#!/usr/bin/env python
# -*- coding: utf-8 -*-


from PySide6.QtGui import QIcon, QPixmap, QFontDatabase
import os
import sys
import traceback
from pathlib import Path


# 检查是否在PyInstaller环境中运行
def is_pyinstaller_bundle():
    return getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')


# 安全获取资源目录路径
def get_resources_dir():
    try:
        if is_pyinstaller_bundle():
            # PyInstaller环境中的资源路径
            if getattr(sys, '_MEIPASS', None):
                base_dir = sys._MEIPASS
            else:
                base_dir = os.path.dirname(sys.executable)
                
            # 尝试多种可能的路径
            possible_paths = [
                os.path.join(base_dir, "resources"),
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    return path
                    
            # 如果都不存在，返回第一个路径并记录警告
            print(f"警告: 无法找到资源目录，将使用默认路径: {possible_paths[0]}")
            return possible_paths[0]
        else:
            # 正常开发环境中的资源路径
            return os.path.join(Path(__file__).parent.parent.parent, "resources")
    except Exception as e:
        print(f"获取资源目录时出错: {e}")
        traceback.print_exc()
        # 返回一个安全的默认值
        return os.path.join(os.getcwd(), "resources")


# 设置资源目录
RESOURCES_DIR = get_resources_dir()


# 安全导入资源模块
HAS_RESOURCE_MODULE = False
try:
    from resources import resources_rc
    HAS_RESOURCE_MODULE = True
except ImportError:
    try:
        import resources_rc
        HAS_RESOURCE_MODULE = True
    except ImportError:
        HAS_RESOURCE_MODULE = False
        print("Warning: resources_rc.py not found. Using direct file access instead.")
        print("Run 'python compile_resources.py' to compile resources.")


def debug_resource_path(resource_name):
    print(f"RESOURCES_DIR: {RESOURCES_DIR}")
    print(f"Resource name: {resource_name}")
    print(f"Is PyInstaller: {is_pyinstaller_bundle()}")
    print(f"Has resource module: {HAS_RESOURCE_MODULE}")
    if is_pyinstaller_bundle():
        print(f"MEIPASS: {getattr(sys, '_MEIPASS', 'Not found')}")
    print(f"Executable dir: {os.path.dirname(sys.executable)}")
    if HAS_RESOURCE_MODULE:
        print(f"Resource path via Qt: {':/images/' + resource_name}")
    else:
        print(f"Resource path via file: {os.path.join(RESOURCES_DIR, 'images', resource_name)}")
    
    file_path = os.path.join(RESOURCES_DIR, 'images', resource_name)
    exists = os.path.exists(file_path)
    print(f"File exists: {exists} (Path: {file_path})")


def get_icon(name):
    """
    Get an icon from resources.

    Args:
        name (str): Icon name (without path or extension)

    Returns:
        QIcon: The requested icon
    """
    try:
        icon = QIcon()
        
        # 首先尝试使用Qt资源系统
        if HAS_RESOURCE_MODULE:
            resource_path = f":/icons/{name}"
            icon = QIcon(resource_path)
            if not icon.isNull():
                return icon
        
        # 尝试从文件系统加载
        # 尝试多种可能的路径
        possible_paths = [
            os.path.join(RESOURCES_DIR, "icons", name),
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return QIcon(path)
                
        # 如果都失败了，返回空图标但记录错误
        print(f"Warning: Icon '{name}' not found in resources or file system.")
        return icon
    except Exception as e:
        print(f"加载图标时出错 '{name}': {e}")
        traceback.print_exc()
        return QIcon()


def get_image(name, child_folder_name=None):
    """
    Get an image from resources.

    Args:
        child_folder_name (str):
        name (str): Image name (without path or extension)

    Returns:
        QPixmap: The requested image
    """
    try:
        # 开启这个调试以查看资源加载详情
        # debug_resource_path(name)
        
        img = QPixmap()
        
        # 首先尝试使用Qt资源系统
        if HAS_RESOURCE_MODULE:
            resource_path = f":/images/{child_folder_name}/{name}" if child_folder_name else f":/images/{name}"
            img = QPixmap(resource_path)
            if not img.isNull():
                return img
        
        # 如果Qt资源加载失败，尝试从文件系统加载
        possible_paths = []
        if child_folder_name:
            possible_paths.extend([
                os.path.join(RESOURCES_DIR, "images", child_folder_name, name),
                os.path.join(RESOURCES_DIR, "images", name),
            ])
        else:
            possible_paths.extend([
                os.path.join(RESOURCES_DIR, "images", name),
                os.path.join(RESOURCES_DIR, name),
            ])
            
        for path in possible_paths:
            if os.path.exists(path):
                img = QPixmap(path)
                if not img.isNull():
                    return img
        
        # 两种方法都失败，记录错误并返回空图片
        print(f"Error: Image '{name}' not found. Tried paths: {possible_paths}")
        return img
    except Exception as e:
        print(f"加载图像时出错 '{name}': {e}")
        traceback.print_exc()
        return QPixmap()


def get_image_path(name, child_folder_name=None):
    """
    Get the path to an image resource.

    Args:
        name (str): Image name (without path or extension)
        child_folder_name (str):

    Returns:
        str: The path to the image
    """
    try:
        possible_paths = []
        if child_folder_name:
            possible_paths.extend([
                os.path.join(RESOURCES_DIR, "images", child_folder_name, name),
                os.path.join(RESOURCES_DIR, "images", name),
            ])
        else:
            possible_paths.extend([
                os.path.join(RESOURCES_DIR, "images", name),
                os.path.join(RESOURCES_DIR, name),
            ])
            
        for path in possible_paths:
            if os.path.exists(path):
                return path
                
        # 如果找不到，返回第一个路径
        return possible_paths[0]
    except Exception as e:
        print(f"获取图像路径时出错 '{name}': {e}")
        traceback.print_exc()
        return os.path.join(RESOURCES_DIR, "images", name)


def load_fonts():
    """
    加载应用程序字体

    从资源目录加载字体文件并注册到应用程序

    Returns:
        dict: 包含已加载字体的信息，键为字体名称，值为字体ID
    """
    loaded_fonts = {}
    try:
        # 首先尝试从Qt资源系统加载
        if HAS_RESOURCE_MODULE:
            font_files = {
                "AlibabaPuHuiTi-2-45-Light": [
                    ":/fonts/AlibabaPuHuiTi-2-45-Light.ttf",
                    ":/fonts/AlibabaPuHuiTi-2-45-Light.otf"
                ]
            }

            for font_name, paths in font_files.items():
                for path in paths:
                    font_id = QFontDatabase.addApplicationFont(path)
                    if font_id != -1:
                        loaded_fonts[font_name] = font_id
                        print(f"成功加载字体: {font_name}")
                        break

        # 如果Qt资源加载失败，尝试从文件系统加载
        if not loaded_fonts:
            # 尝试多种可能的路径
            possible_font_dirs = [
                os.path.join(RESOURCES_DIR, "fonts"),
            ]

            for font_dir in possible_font_dirs:
                if os.path.exists(font_dir):
                    # 查找字体文件
                    font_files = []
                    for ext in ['.ttf', '.otf']:
                        font_files.extend([
                            os.path.join(font_dir, f) for f in os.listdir(font_dir)
                            if f.endswith(ext)
                        ])

                    # 加载找到的字体文件
                    for font_file in font_files:
                        font_id = QFontDatabase.addApplicationFont(font_file)
                        if font_id != -1:
                            font_name = os.path.basename(font_file).split('.')[0]
                            loaded_fonts[font_name] = font_id
                            print(f"成功从文件系统加载字体: {font_name}")

        # 打印已加载的字体族
        if loaded_fonts:
            for font_id in loaded_fonts.values():
                families = QFontDatabase.applicationFontFamilies(font_id)
                print(f"已加载字体族: {', '.join(families)}")
        else:
            print("警告: 未能加载任何字体")

        return loaded_fonts
    except Exception as e:
        print(f"加载字体时出错: {e}")
        traceback.print_exc()
        return {}


def print_resource_debug_info():
    """打印资源加载的调试信息，用于排查问题"""
    try:
        print("\n=== 资源加载调试信息 ===")
        print(f"当前工作目录: {os.getcwd()}")
        print(f"资源目录路径: {RESOURCES_DIR}")
        print(f"资源目录存在: {os.path.exists(RESOURCES_DIR)}")

        if os.path.exists(RESOURCES_DIR):
            print("\n资源目录内容:")
            try:
                for root, dirs, files in os.walk(RESOURCES_DIR):
                    level = root.replace(RESOURCES_DIR, '').count(os.sep)
                    indent = ' ' * 4 * level
                    print(f"{indent}{os.path.basename(root)}/")
                    sub_indent = ' ' * 4 * (level + 1)
                    for f in files:
                        print(f"{sub_indent}{f}")
            except Exception as e:
                print(f"列出资源目录内容时出错: {e}")

        print(f"\n是否PyInstaller环境: {is_pyinstaller_bundle()}")
        print(f"已加载资源模块: {HAS_RESOURCE_MODULE}")

        # 测试几个关键图标是否可以加载
        test_images = ["home.svg", "settings.svg", "app_logo.png"]
        print("\n测试图像加载:")
        for img_name in test_images:
            try:
                px = get_image(img_name)
                print(f"  {img_name}: {'成功' if not px.isNull() else '失败'}")
            except Exception as e:
                print(f"  {img_name}: 加载出错 - {e}")

        print("=== 调试信息结束 ===\n")
    except Exception as e:
        print(f"打印资源调试信息时出错: {e}")
        traceback.print_exc()
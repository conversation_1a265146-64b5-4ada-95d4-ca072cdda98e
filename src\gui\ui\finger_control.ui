<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>273</width>
    <height>411</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <property name="spacing">
    <number>2</number>
   </property>
   <property name="leftMargin">
    <number>15</number>
   </property>
   <property name="topMargin">
    <number>15</number>
   </property>
   <property name="rightMargin">
    <number>15</number>
   </property>
   <property name="bottomMargin">
    <number>15</number>
   </property>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <property name="spacing">
      <number>2</number>
     </property>
     <item>
      <widget class="QToolButton" name="toolButton_dot">
       <property name="minimumSize">
        <size>
         <width>15</width>
         <height>15</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>15</width>
         <height>15</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">border: 1px solid black; border-radius: 7px;</string>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QTextEdit" name="textEdit_name">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>39</height>
        </size>
       </property>
       <property name="sizeIncrement">
        <size>
         <width>0</width>
         <height>39</height>
        </size>
       </property>
       <property name="html">
        <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;meta charset=&quot;utf-8&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
hr { height: 1px; border-width: 0; }
li.unchecked::marker { content: &quot;\2610&quot;; }
li.checked::marker { content: &quot;\2612&quot;; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'Microsoft YaHei UI'; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot;-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;br /&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="1,0">
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <layout class="QVBoxLayout" name="verticalLayout">
         <property name="spacing">
          <number>0</number>
         </property>
         <item>
          <widget class="QLabel" name="label">
           <property name="styleSheet">
            <string notr="true">color: gray;font-size: 16px;</string>
           </property>
           <property name="text">
            <string>Speed</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QDoubleSpinBox" name="doubleSpinBox_speed">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="styleSheet">
            <string notr="true">border-radius: 5px</string>
           </property>
           <property name="buttonSymbols">
            <enum>QAbstractSpinBox::UpDownArrows</enum>
           </property>
           <property name="decimals">
            <number>1</number>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer_2">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Fixed</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>15</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="label_2">
           <property name="styleSheet">
            <string notr="true">color: gray;font-size: 16px;</string>
           </property>
           <property name="text">
            <string>Force</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QDoubleSpinBox" name="doubleSpinBox_force">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="styleSheet">
            <string notr="true">border-radius: 2px</string>
           </property>
           <property name="buttonSymbols">
            <enum>QAbstractSpinBox::UpDownArrows</enum>
           </property>
           <property name="decimals">
            <number>1</number>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Fixed</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>15</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="label_3">
           <property name="styleSheet">
            <string notr="true">color: gray;font-size: 16px;</string>
           </property>
           <property name="text">
            <string>Position</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QDoubleSpinBox" name="doubleSpinBox_position">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="styleSheet">
            <string notr="true">border-radius: 2px</string>
           </property>
           <property name="buttonSymbols">
            <enum>QAbstractSpinBox::UpDownArrows</enum>
           </property>
           <property name="decimals">
            <number>1</number>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,0">
         <item>
          <widget class="FingerSliderH" name="frame_slider_bottom">
           <property name="frameShape">
            <enum>QFrame::NoFrame</enum>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_zw">
           <property name="frameShape">
            <enum>QFrame::NoFrame</enum>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </item>
     <item>
      <widget class="FingerSliderV" name="frame_slider_right">
       <property name="frameShape">
        <enum>QFrame::NoFrame</enum>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>FingerSliderV</class>
   <extends>QFrame</extends>
   <header>..components.custom_slider</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>FingerSliderH</class>
   <extends>QFrame</extends>
   <header>..components.custom_slider</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>

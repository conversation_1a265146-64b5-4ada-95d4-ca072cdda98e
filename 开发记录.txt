已设置全局字体: Alibaba PuHuiTi 2.0 45 Light, 大小: 10
2025-07-27 13:27:44.595 | INFO     | src.core.sdk_manager:connect_device:529 - 正在连接: COM44, 波特率: Baudrate.Baud460800
2025-07-27 13:27:44.806 | INFO     | src.gui.main_window:_on_sdk_connect_result:632 - 设备连接状态: COM44, 连接状态: True, MSG:
slave[126] read_holding_registers(address: 1099, count: 1) timeout, retry 0/3

thread 'tokio-runtime-worker' panicked at src\stark\api.rs:1388:17:
index out of bounds: the len is 1 but the index is 1
note: run with `RUST_BACKTRACE=1` environment variable to display a backtrace
Traceback (most recent call last):
  File "E:\BrainCo_Revo_hand_tool\src\core\sdk_manager.py", line 153, in execute_command
    return await method(*args)
pyo3_async_runtimes.RustPanic: rust future panicked: unknown error

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\BrainCo_Revo_hand_tool\src\utils\async_runner.py", line 52, in wrapped_coro
    result = await coro
  File "E:\BrainCo_Revo_hand_tool\src\core\sdk_manager.py", line 817, in _get_all_info_sequential
    result = await hand_device.execute_command("get_action_sequence", slave_id, action_id.to_sdk_type())
  File "E:\BrainCo_Revo_hand_tool\src\core\sdk_manager.py", line 155, in execute_command
    raise Exception(f"Function {method_name} Execution Failed: {str(e)}")
Exception: Function get_action_sequence Execution Failed: rust future panicked: unknown error
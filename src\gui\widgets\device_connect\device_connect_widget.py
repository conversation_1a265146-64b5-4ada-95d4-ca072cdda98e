from PySide6.QtCore import Qt, QPoint, QSize, Signal
from PySide6.QtGui import <PERSON><PERSON>ain<PERSON>
from PySide6.QtWidgets import Q<PERSON>ush<PERSON>utton, QHBoxLayout, QLabel, QFrame, QTabWidget, QBoxLayout

from src.core.hand_specifications import HandVersion, HandSpecification, ProtocolType, INTERFACE_CONNECTION_PARAMETERS, HandType
from src.utils.resource_helper import get_image
from .base import ProtocolWidgetBase
from .modbus_panel import ModbusPanel
from .param_widget import SelectParamWidget
from ...components.base_widgets import BorderlessWidgetBase, BorderlessLayout
from ...ui.connect_panel import Ui_Form

__all__ = ["DeviceConnectWidget"]

from ...ui_config import ID_TIP_MSG

_STYLE = """
font-family: 'Alibaba PuHuiTi 2.0';
color: {};
font-size: {};
font-weight: {};
letter-spacing: 0px;  /* 可能不支持，依赖平台 */
"""

_HAND_VERSION_NAME_MAP = {HandVersion.V2: "Revo 2"}


class HandImageLabel(QLabel):
    def __init__(self, pixmap, parent=None):
        super().__init__(parent)
        self.pixmap = pixmap
        self.position = QPoint(0, 0)  # 默认位置
        self.scaled_size = None  # 默认不缩放
        # 确保QLabel不会自动设置大小
        self.setMinimumSize(1, 1)

    def set_image_position(self, x, y):
        self.position = QPoint(x, y)
        self.update()  # 更新绘图
        
    def set_image_size(self, width, height):
        self.scaled_size = QSize(width, height)
        self.update()  # 更新绘图

    def paintEvent(self, event):
        # 首先调用父类的paintEvent以绘制QLabel本身
        super().paintEvent(event)
        
        if not self.pixmap or self.pixmap.isNull():
            return
            
        painter = QPainter(self)
        
        # 如果设置了缩放大小，则缩放图像
        if self.scaled_size:
            scaled_pixmap = self.pixmap.scaled(
                self.scaled_size.width(), 
                self.scaled_size.height(),
                Qt.KeepAspectRatio, 
                Qt.SmoothTransformation
            )
            painter.drawPixmap(self.position, scaled_pixmap)
        else:
            # 使用原始大小
            painter.drawPixmap(self.position, self.pixmap)



class HandConnectWidget(BorderlessWidgetBase):
    """显示手图片以及连接界面"""

    def __init__(self, hand_version: HandVersion, parent=None):
        super().__init__(parent)
        self.hand_version = hand_version

        self.setLayout(BorderlessLayout(QBoxLayout.Direction.LeftToRight))
        self.layout().setSpacing(40)

        # 设置图片
        if self.hand_version is HandVersion.V2:
            self.hand_img_label = HandImageLabel(get_image("revo2_hand.png"))
            self.hand_img_label.set_image_position(0, -60)
            self.hand_img_label.set_image_size(276, 607)
        self.hand_img_label.setFixedSize(276, 607)
        self.tab_widget = QTabWidget()
        self.tab_widget.setContentsMargins(0, 0, 0, 0)
        self.tab_widget.tabBar().hide()

        self.choose_protocol_type_widget = SelectParamWidget("Protocol", [],  self)

        self.layout().addWidget(self.hand_img_label)

        v_layout = BorderlessLayout(QBoxLayout.Direction.TopToBottom)
        v_layout.setSpacing(7)
        v_layout.addStretch()
        v_layout.addWidget(self.choose_protocol_type_widget)
        v_layout.addWidget(self.tab_widget)
        v_layout.addStretch()
        self.layout().addLayout(v_layout)
        self._init_ui()

    def _init_ui(self):
        # 设置连接窗口
            for protocol_type in INTERFACE_CONNECTION_PARAMETERS["supported_protocols"]:
                if protocol_type == ProtocolType.Modbus:
                    config = INTERFACE_CONNECTION_PARAMETERS["protocol_parameters"][ProtocolType.Modbus]
                    modbus_params_config = {
                        "id_range": config.slave_address_range,
                        "default_id": config.slave_address,
                        "baudrate_list": config.supported_baudrates,
                        "default_baudrate": config.default_baudrate,
                        "id_tips": ID_TIP_MSG[self.hand_version]["English"]
                    }

                    widget = ModbusPanel(modbus_params_config)
                    self._add_param_widget(protocol_type, widget)

    def _add_param_widget(self, protocol_type: ProtocolType, protocol_widget):
        self.choose_protocol_type_widget.input.addItem(protocol_type.name, protocol_type)
        self.tab_widget.addTab(protocol_widget, protocol_type.name)


class DeviceConnectWidget(QFrame, Ui_Form):
    connect_signal = Signal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUi(self)

        self._cur_hand_version = HandVersion.V2
        self._cur_hand_type = HandType.Left

        self._hand_version_buttons = []

        self.setStyleSheet("background-color: #2B3134; border: none; border-radius: 8px;")

        self._init_ui()

    def _init_ui(self):

        # 设置主界面
        self.tabWidget_hand_version.tabBar().hide()
        self.tabWidget_hand_version.setContentsMargins(0, 0, 0, 0)
        self.setStyleSheet("background-color: #2B3134; border: none; border-radius: 8px;")

        # 设置 Connect to the Revo world 标签
        self.label_connect_world.resize(289, 34)
        self.label_connect_world.setStyleSheet(_STYLE.format("#FFFFFF", "24px", 300))

        # 设置小手图标
        self.label_hand_sm.resize(40, 40)
        self.label_hand_sm.setStyleSheet("border-radius: 8px; background-color: #414648;")
        pix = get_image("vector.svg")
        pix = pix.scaled(28, 28, Qt.KeepAspectRatio)
        self.label_hand_sm.setPixmap(pix)

        # 设置连接按钮
        self.pushButton_connect.setFixedSize(200, 58)
        self.pushButton_connect.setStyleSheet(_STYLE.format("#27252E", "20px", 700) + "background-color: #FF9429; border-radius: 8px; padding: 14 24 14 24;")
        self.pushButton_connect.clicked.connect(self._on_connect_button)

        # 选择手版本按钮
        self.widget_hand_version.resize(224, 48)
        self.widget_hand_version.setFixedHeight(48)
        self.widget_hand_version.setStyleSheet("border: none; border-radius: 8px; padding: 4 4 4 4;background-color: #3F4749;")
        self.widget_hand_version.setLayout(QHBoxLayout())
        self.widget_hand_version.layout().setSpacing(0)
        self.widget_hand_version.layout().setContentsMargins(4, 0, 4, 0)
        for h_version in HandVersion:
            name = _HAND_VERSION_NAME_MAP[h_version]
            button = QPushButton(name)
            button.setFixedSize(108, 40)
            button.setCheckable(True)
            button.setStyleSheet("""
            QPushButton {
                font-family: 'Alibaba PuHuiTi 2.0';
                font-weight: 300;
                font-size: 20px;
                background-color: #3F4749;
                color: white;
                border: none;
            }
            QPushButton:checked {
                border: none solid white;
                background-color: #52595A;
            }
            """)
            self._hand_version_buttons.append(button)
            self.widget_hand_version.layout().addWidget(button)
            button.clicked.connect(self._on_hand_version_button_clicked)

            hand_connect_widget = HandConnectWidget(h_version)
            button.setProperty("hand_version", h_version)
            button.setProperty("widget", hand_connect_widget)
            self.tabWidget_hand_version.addTab(hand_connect_widget, "")

            if h_version == HandVersion.V2:  # 默认进入二代手连接界面
                button.setChecked(True)
                self.tabWidget_hand_version.setCurrentWidget(hand_connect_widget)

    def _on_hand_version_button_clicked(self):
        self.tabWidget_hand_version.setCurrentWidget(self.sender().property("widget"))
        for button in self._hand_version_buttons:
            if button.text() == self.sender().text():
                self._cur_hand_version = button.property("hand_version")
                button.setChecked(True)
            else:
                button.setChecked(False)

    def _on_connect_button(self):
        connect_config = self._get_connect_config()
        self.connect_signal.emit(connect_config)

    def _get_connect_config(self):
        hand_connect_widget = self.tabWidget_hand_version.currentWidget()
        if isinstance(hand_connect_widget, HandConnectWidget):
            protocol_widget = hand_connect_widget.tab_widget.currentWidget()
            if isinstance(protocol_widget, ProtocolWidgetBase):
                config = protocol_widget.get_config()
                config["protocol_type"] = hand_connect_widget.choose_protocol_type_widget.get_data()
                config["hand_version"] = self._cur_hand_version
                config["hand_type"] = self._cur_hand_type
                return config
        return None


class DeviceConnectDialog(DeviceConnectWidget):
    def __init__(self, parent=None):
        super().__init__(parent)

        # 设置为dialog属性
        self.setWindowFlags(self.windowFlags() | Qt.Dialog | Qt.FramelessWindowHint)

        self.setStyleSheet("background-color: #2D2E33; border: none; border-radius: 8px;")

    def show_dialog(self, hand_version, hand_type):
        self._cur_hand_version = hand_version
        self._cur_hand_type = hand_type

        for button in self._hand_version_buttons:
            if button.text() != _HAND_VERSION_NAME_MAP[self._cur_hand_version]:
                button.setVisible(False)

        self.show()

    def _on_connect_button(self):
        connect_config = self._get_connect_config()
        self.close()
        self.connect_signal.emit(connect_config)



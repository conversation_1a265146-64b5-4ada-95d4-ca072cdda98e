#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
字体管理器模块

提供字体加载、管理和应用的功能
"""

from PySide6.QtGui import QFontDatabase, QFont
from PySide6.QtWidgets import QApplication
import os
import traceback
from .resource_helper import RESOURCES_DIR, HAS_RESOURCE_MODULE


class FontManager:
    """字体管理器类"""
    
    def __init__(self):
        self.loaded_fonts = {}
        self.available_families = []
        
    def load_application_fonts(self):
        """
        加载应用程序字体
        
        Returns:
            bool: 是否成功加载字体
        """
        try:
            # 首先尝试从Qt资源系统加载
            if HAS_RESOURCE_MODULE:
                success = self._load_fonts_from_resources()
                if success:
                    return True
            
            # 如果Qt资源加载失败，尝试从文件系统加载
            print("如果Qt资源加载失败，尝试从文件系统加载")
            return self._load_fonts_from_filesystem()
            
        except Exception as e:
            print(f"加载字体时出错: {e}")
            traceback.print_exc()
            return False
    
    def _load_fonts_from_resources(self):
        """从Qt资源系统加载字体"""
        font_files = {
            "AlibabaPuHuiTi-2-45-Light": [
                ":/fonts/AlibabaPuHuiTi-2-45-Light.ttf",
                ":/fonts/AlibabaPuHuiTi-2-45-Light.otf"
            ]
        }
        
        success = False
        for font_name, paths in font_files.items():
            for path in paths:
                font_id = QFontDatabase.addApplicationFont(path)
                if font_id != -1:
                    self.loaded_fonts[font_name] = font_id
                    families = QFontDatabase.applicationFontFamilies(font_id)
                    self.available_families.extend(families)
                    print(f"成功从Qt资源加载字体: {font_name} -> {', '.join(families)}")
                    success = True
                    break
        
        return success
    
    def _load_fonts_from_filesystem(self):
        """从文件系统加载字体"""
        possible_font_dirs = [
            os.path.join(RESOURCES_DIR, "fonts"),
            os.path.join("resources", "fonts")
        ]
        
        success = False
        for font_dir in possible_font_dirs:
            if os.path.exists(font_dir):
                # 查找字体文件
                font_files = []
                for ext in ['.ttf', '.otf']:
                    try:
                        font_files.extend([
                            os.path.join(font_dir, f) for f in os.listdir(font_dir) 
                            if f.lower().endswith(ext)
                        ])
                    except OSError:
                        continue
                
                # 加载找到的字体文件
                for font_file in font_files:
                    font_id = QFontDatabase.addApplicationFont(font_file)
                    if font_id != -1:
                        font_name = os.path.basename(font_file).split('.')[0]
                        self.loaded_fonts[font_name] = font_id
                        families = QFontDatabase.applicationFontFamilies(font_id)
                        self.available_families.extend(families)
                        print(f"成功从文件系统加载字体: {font_name} -> {', '.join(families)}")
                        success = True
        
        return success
    
    def get_available_font_families(self):
        """获取可用的字体族列表"""
        return self.available_families.copy()
    
    def set_application_font(self, app=None, font_family=None, font_size=10):
        """
        设置应用程序全局字体
        
        Args:
            app: QApplication实例，如果为None则自动获取
            font_family: 字体族名称，如果为None则使用默认优先级
            font_size: 字体大小，默认10
            
        Returns:
            bool: 是否成功设置字体
        """
        try:
            if app is None:
                app = QApplication.instance()
                if app is None:
                    print("错误: 无法获取QApplication实例")
                    return False
            
            font = app.font()
            
            # 如果指定了字体族名称
            if font_family:
                if font_family in self.available_families:
                    font.setFamily(font_family)
                    font.setPointSize(font_size)
                    app.setFont(font)
                    print(f"已设置全局字体: {font_family}, 大小: {font_size}")
                    return True
                else:
                    print(f"警告: 指定的字体族 '{font_family}' 不可用")
            
            # 使用默认优先级设置字体
            preferred_fonts = [
                "Alibaba PuHuiTi 2.0",
                "AlibabaPuHuiTi-2-45-Light",
                "Alibaba PuHuiTi",
                "PingFang SC",
                "Microsoft YaHei",
                "SimHei"
            ]
            
            for font_name in preferred_fonts:
                if font_name in self.available_families:
                    font.setFamily(font_name)
                    font.setPointSize(font_size)
                    app.setFont(font)
                    print(f"已设置全局字体: {font_name}, 大小: {font_size}")
                    return True
            
            # 如果没有找到首选字体，使用第一个可用的字体
            if self.available_families:
                font.setFamily(self.available_families[0])
                font.setPointSize(font_size)
                app.setFont(font)
                print(f"已设置全局字体: {self.available_families[0]}, 大小: {font_size}")
                return True
            
            # 最后尝试设置系统字体
            font.setFamily("Microsoft YaHei")
            font.setPointSize(font_size)
            app.setFont(font)
            print(f"使用系统默认字体: Microsoft YaHei, 大小: {font_size}")
            return True
            
        except Exception as e:
            print(f"设置应用程序字体时出错: {e}")
            traceback.print_exc()
            return False
    
    def create_font(self, family=None, size=10, weight=QFont.Weight.Normal, italic=False):
        """
        创建字体对象
        
        Args:
            family: 字体族名称，如果为None则使用第一个可用字体
            size: 字体大小
            weight: 字体粗细
            italic: 是否斜体
            
        Returns:
            QFont: 字体对象
        """
        try:
            if family is None and self.available_families:
                family = self.available_families[0]
            elif family is None:
                family = "Microsoft YaHei"
            
            font = QFont(family, size, weight, italic)
            return font
        except Exception as e:
            print(f"创建字体对象时出错: {e}")
            return QFont()
    
    def print_font_info(self):
        """打印字体信息"""
        try:
            print("\n=== 字体管理器信息 ===")
            print(f"已加载字体数量: {len(self.loaded_fonts)}")
            print(f"可用字体族数量: {len(self.available_families)}")
            
            if self.loaded_fonts:
                print("\n已加载的字体:")
                for font_name, font_id in self.loaded_fonts.items():
                    families = QFontDatabase.applicationFontFamilies(font_id)
                    print(f"  {font_name} (ID: {font_id}) -> {', '.join(families)}")
            
            if self.available_families:
                print(f"\n可用字体族: {', '.join(self.available_families)}")
            
            print("=== 字体信息结束 ===\n")
        except Exception as e:
            print(f"打印字体信息时出错: {e}")


# 全局字体管理器实例
_font_manager = None


def get_font_manager():
    """获取全局字体管理器实例"""
    global _font_manager
    if _font_manager is None:
        _font_manager = FontManager()
    return _font_manager


def setup_application_fonts(app=None):
    """
    设置应用程序字体的便捷函数
    
    Args:
        app: QApplication实例
        
    Returns:
        bool: 是否成功设置字体
    """
    font_manager = get_font_manager()
    
    # 加载字体
    if not font_manager.load_application_fonts():
        print("警告: 字体加载失败")
    
    # 设置全局字体
    return font_manager.set_application_font(app)

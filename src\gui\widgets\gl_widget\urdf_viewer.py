import os

import trimesh
from PySide6.QtGui import QColor, QVector3D
from PySide6.QtWidgets import QWidget, QVBoxLayout, QApplication, QFrame
from PySide6.QtCore import Qt, QThread, Signal, QTimer
import pyqtgraph.opengl as gl
import numpy as np
import stl
import xml.etree.ElementTree as ET
from urdf_parser_py.urdf import URDF

from src import APP_ROOT_PATH
from src.gui.widgets.gl_widget.core.kinematics import SimpleTransformCalculator
from src.gui.widgets.gl_widget.core.urdf_parser import URDFPathConverter, FingerGroupAnalyzer, URDFJointAnalyzer


class URDFLoadWorker(QThread):
    progress_updated = Signal(str)  # 进度更新信号
    loading_finished = Signal(object, str, str)  # 加载完成信号 (robot, urdf_dir, package_dir)
    loading_failed = Signal(str)  # 加载失败信号

    def __init__(self, file_path, workspace_root):
        super().__init__()
        self.file_path = file_path
        self.workspace_root = workspace_root
        self.robot = None
        self.urdf_dir = None
        self.package_dir = None

    def run(self):
        try:
            print("start loading urdf file")
            # 初始化路径转换器
            path_converter = URDFPathConverter(self.workspace_root)

            self.progress_updated.emit("正在检查URDF文件...")

            # 检查URDF文件是否包含需要转换的ROS package路径
            converted_urdf_path = self.file_path
            with open(self.file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if "package://" in content:
                    self.progress_updated.emit("检测到ROS package路径，正在转换...")
                    output_path = self.file_path.replace('.urdf', '_converted.urdf')
                    converted_urdf_path = path_converter.convert_urdf_file(self.file_path, output_path)

            # 存储重要的目录路径信息
            self.urdf_dir = os.path.dirname(os.path.abspath(self.file_path))
            self.package_dir = os.path.dirname(self.urdf_dir)

            self.progress_updated.emit("正在解析URDF文件...")

            # 解析URDF文件，创建机器人模型对象
            self.robot = URDF.from_xml_file(converted_urdf_path)

            self.progress_updated.emit(f"成功解析URDF文件。找到 {len(self.robot.links)} 个链接。")
            # 发送加载完成信号
            self.loading_finished.emit(self.robot, self.urdf_dir, self.package_dir)

        except Exception as e:
            error_msg = f"加载URDF文件失败: {str(e)}"
            print(error_msg)
            import traceback
            print(traceback.format_exc())
            self.loading_failed.emit(error_msg)


class URDFViewer(QFrame):
    loading_finished = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.path_converter = None           # 路径转换器

        self.robot = None                    # URDF机器人模型对象
        self.mesh_items = []                 # 3D网格显示项列表
        self.joint_items = {}                # 关节显示项字典
        self.camera_distance = 10.0          # 相机距离
        self.camera_rotation = 0             # 相机旋转角度
        self.urdf_dir = None                 # URDF文件目录
        self.package_dir = None              # ROS包目录

        # 相机状态管理
        self.camera_state = {
            'distance': 10.0,
            'elevation': 30.0,
            'azimuth': 45.0,
            'center': [0, 0, 0]
        }
        self.view_update_blocked = False     # 防止视图更新冲突的标志

        # 颜色设置
        self.link_color = QColor(180, 180, 180, 255)  # 默认链接颜色（灰色）
        self.joint_color = QColor(255, 0, 0, 255)     # 默认关节颜色（红色）

        # 关节控制相关
        self.joint_sliders = {}              # 关节滑块控件字典
        self.joint_transforms = {}           # 关节变换矩阵缓存
        self.link_transforms = {}            # 链接变换矩阵缓存
        self.mesh_data = {}                  # 网格数据缓存
        self.joint_values = {}               # 当前关节值

        # 分析器和控制器实例
        self.joint_analyzer = None           # 关节分析器
        self.finger_group_analyzer = None    # 手指分组分析器
        self.path_converter = None           # 路径转换器

        # 材质属性管理
        self.current_material_type = "Default"
        self.material_properties = {
            "Metallic": {"roughness": 0.5, "metalness": 0.5},
            "Plastic": {"specular": 0.5},
            "Glass": {"transparency": 0.5}
        }

        # URDF材质信息
        self.urdf_materials = {}             # URDF中定义的材质
        self.link_materials = {}             # 每个链接的材质信息

        # 性能优化变量
        self.transform_calculator = None     # 变换计算器
        self.update_timer = QTimer()         # 更新定时器
        self.update_timer.setSingleShot(True)
        self.update_timer.setInterval(16)    # 60FPS更新频率
        self.update_timer.timeout.connect(self._update_view)
        self.pending_update = False          # 待更新标志
        self.vertex_cache = {}               # 顶点缓存
        self.visual_transforms = {}          # 视觉变换缓存

        # 手指组控制变量
        self.finger_group_sliders = {}       # 手指组滑块
        self.finger_group_vars = {}          # 手指组变量
        self.individual_joint_sliders = {}   # 独立关节滑块
        self.individual_joint_vars = {}      # 独立关节变量

        # 线程相关变量
        self.load_worker = None              # URDF加载工作线程
        self.is_loading = False              # 是否正在加载标志

        self._setup_ui()
        self._setup_style()

    def _setup_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建3D视图
        self.gl_view = gl.GLViewWidget()
        # self.gl_view.setBackgroundColor("#2D2E35")

        layout.addWidget(self.gl_view)
        self.setLayout(layout)

    def _update_view(self):
        """
        更新3D视图显示

        这是一个轻量级的视图更新函数，用于刷新3D渲染。
        包含视图更新阻塞检查，防止在不合适的时机进行更新。
        通过定时器调用，实现异步更新以保持UI响应性。
        """
        # 检查是否允许更新视图
        if self.view_update_blocked:
            return

        # 清除待更新标志
        self.pending_update = False

        # 刷新3D视图
        self.gl_view.update()

    def _setup_style(self):
        self.setStyleSheet("background-color: #2D2E35;")

    def load_urdf(self, urdf_file_path):
        if self.is_loading:
            return
        if urdf_file_path and os.path.exists(urdf_file_path):
            self.is_loading = True

            self.load_worker = URDFLoadWorker(urdf_file_path, APP_ROOT_PATH)
            self.load_worker.loading_finished.connect(self.on_loading_finished)
            self.load_worker.loading_failed.connect(self.on_loading_failed)
            self.load_worker.start()

    def on_loading_finished(self, robot, urdf_dir, package_dir):
        try:
            # 保存加载结果
            self.robot = robot
            self.urdf_dir = urdf_dir
            self.package_dir = package_dir

            # 使用原始文件路径进行关节分析
            original_file = self.load_worker.file_path
            self.joint_analyzer = URDFJointAnalyzer(original_file)
            self.joint_analyzer.print_joint_info()

            # 检查是否找到可控关节
            if not self.joint_analyzer.get_controllable_joints():
                print("未找到可控关节！")
            else:
                # 创建手指分组分析器
                self.finger_group_analyzer = FingerGroupAnalyzer(self.joint_analyzer)

            converted_urdf_path = original_file.replace('.urdf', '_converted.urdf')
            if os.path.exists(converted_urdf_path):
                self.parse_urdf_materials(converted_urdf_path)
            else:
                self.parse_urdf_materials(original_file)

            # 初始化运动学计算器
            self.transform_calculator = SimpleTransformCalculator(self.robot)

            # 初始化关节值
            self.initialize_joint_values()

            # 预计算视觉变换
            self.precompute_visual_transforms()

            # 加载和显示网格
            self.load_and_display_meshes()

            # # 创建关节控制界面
            # QApplication.processEvents()
            # self.create_joint_controls()

            # # 启用保存图片按钮
            # self.save_image_button.setEnabled(True)

            print(f"URDF文件加载完成")
            self.loading_finished.emit()

        except Exception as e:
            error_msg = f"处理加载结果时出错: {str(e)}"
            print(error_msg)
            import traceback
            print("错误追踪:")
            print(traceback.format_exc())
        finally:
            # 恢复UI状态
            self.is_loading = False
            self.load_worker = None

    def on_loading_failed(self, error_message):
        pass
        # self.status_label.setText(f"加载失败: {error_message}")
        # print(f"URDF加载失败: {error_message}")
        #
        # # 恢复UI状态
        # self.is_loading = False
        # self.load_button.setEnabled(True)
        # self.load_worker = None

    def parse_urdf_materials(self, urdf_path):
        """解析URDF文件中的材质信息"""
        try:
            tree = ET.parse(urdf_path)
            root = tree.getroot()

            # 解析材质定义
            for material in root.iter('material'):
                material_name = material.get('name', '')
                if material_name:
                    color_elem = material.find('color')
                    if color_elem is not None:
                        rgba = color_elem.get('rgba', '1 1 1 1').split()
                        if len(rgba) == 4:
                            self.urdf_materials[material_name] = {
                                'color': [float(x) for x in rgba]
                            }

            # 解析每个链接的材质
            for link in root.iter('link'):
                link_name = link.get('name')
                visual = link.find('visual')
                if visual is not None:
                    material = visual.find('material')
                    if material is not None:
                        material_name = material.get('name', '')
                        color_elem = material.find('color')
                        if color_elem is not None:
                            rgba = color_elem.get('rgba', '1 1 1 1').split()
                            if len(rgba) == 4:
                                self.link_materials[link_name] = {
                                    'name': material_name,
                                    'color': [float(x) for x in rgba]
                                }

            print(f"解析到 {len(self.urdf_materials)} 个材质定义")
            print(f"解析到 {len(self.link_materials)} 个链接材质")

        except Exception as e:
            print(f"解析材质信息错误: {e}")
            self.urdf_materials = {}
            self.link_materials = {}

    def initialize_joint_values(self):
        self.joint_values = {}
        for joint in self.robot.joints:
            if joint.type != 'fixed':
                # 设置初始关节值
                if joint.limit is not None:
                    # 对于旋转关节，设置到范围中间
                    if joint.type == 'revolute':
                        self.joint_values[joint.name] = joint.limit.lower
                        # self.joint_values[joint.name] = (joint.limit.lower + joint.limit.upper) / 2
                    # 对于移动关节，设置到下限
                    elif joint.type == 'prismatic':
                        self.joint_values[joint.name] = joint.limit.lower
                else:
                    self.joint_values[joint.name] = 0.0

        # 将关节值同步到变换计算器
        if self.transform_calculator:
            for joint_name, value in self.joint_values.items():
                self.transform_calculator.joint_values[joint_name] = value
            self.transform_calculator.calculate_all_transforms()

    def precompute_visual_transforms(self):
        """预计算视觉变换"""
        self.visual_transforms = {}
        for link in self.robot.links:
            if link.visual and link.visual.origin:
                try:
                    xyz = np.array(link.visual.origin.xyz)
                    rpy = np.array(link.visual.origin.rpy)

                    # 创建视觉变换矩阵
                    roll, pitch, yaw = rpy
                    c_r, s_r = np.cos(roll), np.sin(roll)
                    c_p, s_p = np.cos(pitch), np.sin(pitch)
                    c_y, s_y = np.cos(yaw), np.sin(yaw)

                    Rx = np.array([[1, 0, 0], [0, c_r, -s_r], [0, s_r, c_r]])
                    Ry = np.array([[c_p, 0, s_p], [0, 1, 0], [-s_p, 0, c_p]])
                    Rz = np.array([[c_y, -s_y, 0], [s_y, c_y, 0], [0, 0, 1]])
                    R = np.dot(np.dot(Rz, Ry), Rx)

                    visual_transform = np.eye(4)
                    visual_transform[:3, :3] = R
                    visual_transform[:3, 3] = xyz

                    self.visual_transforms[link.name] = visual_transform
                except Exception as e:
                    print(f"预计算视觉变换错误 {link.name}: {e}")
                    self.visual_transforms[link.name] = np.eye(4)
            else:
                self.visual_transforms[link.name] = np.eye(4)

    def load_and_display_meshes(self):
        """加载和显示网格"""
        # 首先计算所有链接的初始变换
        link_transforms = {}

        def calculate_link_transform(link_name):
            if link_name in link_transforms:
                return link_transforms[link_name]

            # 查找连接到此链接的关节
            joint = next((j for j in self.robot.joints if j.child == link_name), None)
            if not joint:
                # 如果没有找到关节，这是base_link或固定链接
                transform = np.eye(4)
                link_transforms[link_name] = transform
                return transform

            # 获取父变换
            parent_transform = calculate_link_transform(joint.parent)

            # 计算关节变换
            if joint.origin is not None:
                xyz = joint.origin.xyz
                rpy = joint.origin.rpy

                # 将RPY转换为旋转矩阵
                roll, pitch, yaw = rpy
                Rx = np.array([[1, 0, 0],
                             [0, np.cos(roll), -np.sin(roll)],
                             [0, np.sin(roll), np.cos(roll)]])
                Ry = np.array([[np.cos(pitch), 0, np.sin(pitch)],
                             [0, 1, 0],
                             [-np.sin(pitch), 0, np.cos(pitch)]])
                Rz = np.array([[np.cos(yaw), -np.sin(yaw), 0],
                             [np.sin(yaw), np.cos(yaw), 0],
                             [0, 0, 1]])
                origin_rotation = np.dot(np.dot(Rz, Ry), Rx)
            else:
                xyz = np.zeros(3)
                origin_rotation = np.eye(3)

            # 获取关节值
            joint_value = self.joint_values.get(joint.name, 0.0)

            # 创建关节旋转矩阵
            axis = np.array(joint.axis)
            axis = axis / np.linalg.norm(axis)

            c = np.cos(joint_value)
            s = np.sin(joint_value)
            t = 1 - c
            x, y, z = axis

            joint_rotation = np.array([
                [t*x*x + c,    t*x*y - s*z,  t*x*z + s*y],
                [t*x*y + s*z,  t*y*y + c,    t*y*z - s*x],
                [t*x*z - s*y,  t*y*z + s*x,  t*z*z + c]
            ])

            # 创建关节原点变换
            origin_transform = np.eye(4)
            origin_transform[:3, :3] = origin_rotation
            origin_transform[:3, 3] = xyz

            # 创建关节运动变换
            motion_transform = np.eye(4)
            motion_transform[:3, :3] = joint_rotation

            # 正确组合原点变换和运动变换
            joint_transform = np.dot(origin_transform, motion_transform)

            # 计算链接变换
            link_transform = np.dot(parent_transform, joint_transform)
            link_transforms[link_name] = link_transform
            return link_transform

        # 计算所有链接的变换，从根链接开始
        # 查找根链接（没有父关节的链接）
        root_links = []
        for link in self.robot.links:
            has_parent_joint = any(joint.child == link.name for joint in self.robot.joints)
            if not has_parent_joint:
                root_links.append(link.name)

        # 首先计算根链接的变换
        for root_link_name in root_links:
            calculate_link_transform(root_link_name)
            print(f"找到根链接: {root_link_name}")

        # 然后计算所有其他链接的变换
        for link in self.robot.links:
            if link.name not in root_links:
                calculate_link_transform(link.name)

        # 加载和变换网格
        for link in self.robot.links:
            if link.visual is not None:
                geometry = link.visual.geometry

                if hasattr(geometry, 'filename'):
                    mesh_file = geometry.filename

                    # 处理package://路径
                    if mesh_file.startswith('package://'):
                        parts = mesh_file[10:].split('/', 1)
                        if len(parts) == 2:
                            package_name, rel_path = parts
                            rel_path = rel_path.replace('\\', '/')
                            # 在工作空间中查找包
                            workspace_root = os.getcwd()
                            package_path = os.path.join(workspace_root, package_name)
                            mesh_file = os.path.join(package_path, *rel_path.split('/'))
                            print(f"转换package路径: {mesh_file}")

                    # 处理相对路径
                    if not os.path.isabs(mesh_file):
                        mesh_file = os.path.join(self.urdf_dir, mesh_file)

                    mesh_file = os.path.normpath(mesh_file)

                    print(f"尝试加载网格: {mesh_file}")

                    # 检查文件是否存在
                    if not os.path.exists(mesh_file):
                        print(f"警告: 网格文件不存在: {mesh_file}")
                        print(f"链接 {link.name} 的网格将不会显示")
                        continue

                    vertices, faces = self.load_mesh_file(mesh_file)

                    if vertices is not None and faces is not None:
                        # 存储原始网格数据
                        self.mesh_data[link.name] = {
                            'vertices': vertices,
                            'faces': faces
                        }

                        # 获取此链接的初始变换
                        if link.name not in link_transforms:
                            raise KeyError(f"链接 '{link.name}' 的变换缺失。")
                        transform = link_transforms[link.name]

                        # 如果指定了视觉原点变换，则应用它
                        if link.visual is not None and link.visual.origin is not None:
                            xyz = link.visual.origin.xyz
                            rpy = link.visual.origin.rpy
                            if None not in (xyz, rpy):
                                # 将RPY转换为旋转矩阵
                                roll, pitch, yaw = rpy
                                Rx = np.array([[1, 0, 0],
                                             [0, np.cos(roll), -np.sin(roll)],
                                             [0, np.sin(roll), np.cos(roll)]])
                                Ry = np.array([[np.cos(pitch), 0, np.sin(pitch)],
                                             [0, 1, 0],
                                             [-np.sin(pitch), 0, np.cos(pitch)]])
                                Rz = np.array([[np.cos(yaw), -np.sin(yaw), 0],
                                             [np.sin(yaw), np.cos(yaw), 0],
                                             [0, 0, 1]])
                                R = np.dot(np.dot(Rz, Ry), Rx)

                                # 创建并应用视觉变换
                                visual_transform = np.eye(4)
                                visual_transform[:3, :3] = R
                                visual_transform[:3, 3] = xyz
                                transform = np.dot(transform, visual_transform)

                        # 将变换应用到顶点
                        vertices_homogeneous = np.hstack((vertices, np.ones((vertices.shape[0], 1))))
                        vertices_transformed = np.dot(vertices_homogeneous, transform.T)
                        vertices = vertices_transformed[:, :3]

                        # 创建具有改进材质属性的网格项
                        mesh_item = gl.GLMeshItem(
                            vertexes=vertices,
                            faces=faces,
                            smooth=True,
                            drawEdges=False,  # 默认不显示边框，提高视觉效果
                            edgeColor=(0.2, 0.2, 0.2, 0.5),  # 更柔和的边框颜色
                            shader='shaded',
                            glOptions='opaque'
                        )

                        # 设置链接名称
                        mesh_item.link_name = link.name

                        # 根据URDF材质信息设置初始颜色
                        self.apply_link_material(mesh_item, link.name)

                        # 将网格添加到场景
                        self.gl_view.addItem(mesh_item)
                        self.mesh_items.append(mesh_item)
                        print(f"成功加载链接: {link.name}")
                    else:
                        print(f"警告: 无法加载链接 {link.name} 的网格数据")

        # 显示加载统计信息
        total_links = len(self.robot.links)
        loaded_meshes = len(self.mesh_items)
        print(f"\n=== 网格加载统计 ===")
        print(f"总链接数: {total_links}")
        print(f"成功加载的网格: {loaded_meshes}")
        print(f"未加载的链接: {total_links - loaded_meshes}")
        if total_links - loaded_meshes > 0:
            print("未加载的链接可能是因为:")
            print("1. 网格文件不存在")
            print("2. 文件路径错误")
            print("3. 文件格式不支持")
        print("==================\n")

    def load_mesh_file(self, mesh_file):
        """
        加载3D网格文件

        支持多种3D文件格式，优先使用trimesh库，如果失败则回退到numpy-stl。
        这种双重策略确保了对不同格式文件的兼容性。

        Args:
            mesh_file (str): 网格文件路径

        Returns:
            tuple: (vertices, faces) 顶点数组和面数组，失败时返回(None, None)
        """
        try:
            # 策略1：优先使用trimesh库，支持多种格式（STL, OBJ, PLY等）
            try:
                mesh = trimesh.load(mesh_file)
                return mesh.vertices, mesh.faces
            except:
                # 策略2：如果trimesh失败，对STL文件使用numpy-stl库
                if mesh_file.lower().endswith('.stl'):
                    mesh = stl.mesh.Mesh.from_file(mesh_file)
                    # 将三角形向量重塑为顶点数组
                    vertices = mesh.vectors.reshape(-1, 3)
                    # 为每个顶点创建面索引
                    faces = np.arange(len(vertices)).reshape(-1, 3)
                    return vertices, faces
                else:
                    # 不支持的文件格式
                    raise
        except Exception as e:
            print(f"加载网格错误 {mesh_file}: {str(e)}")
            return None, None

    def set_camera_parameters(self, distance=None, azimuth=None, elevation=None,
                              center_x=None, center_y=None, center_z=None):
        """
        一次性设置所有相机参数并生效

        Args:
            distance (float, optional): 相机距离 (1-50)
            azimuth (float, optional): 方位角度 (0-360)
            elevation (float, optional): 仰角度 (-90到90)
            center_x (float, optional): X轴中心位置 (-10.0到10.0)
            center_y (float, optional): Y轴中心位置 (-10.0到10.0)
            center_z (float, optional): Z轴中心位置 (-10.0到10.0)

        Returns:
            dict: 设置后的实际参数值

        Example:
            # 设置为正面视角，距离15，观察中心在(1, 0, 2)
            viewer.set_camera_parameters(
                distance=15,
                azimuth=0,
                elevation=0,
                center_x=1.0,
                center_y=0.0,
                center_z=2.0
            )

            # 只设置距离和角度，保持中心位置不变
            viewer.set_camera_parameters(distance=20, azimuth=90)
        """
        if self.view_update_blocked:
            return None

        self.view_update_blocked = True

        try:
            # 记录原始值用于动画或回退
            original_values = {
                'distance': self.camera_state['distance'],
                'azimuth': self.camera_state['azimuth'],
                'elevation': self.camera_state['elevation'],
                'center': self.camera_state['center'].copy()
            }

            # 更新相机状态（只更新提供的参数）
            if distance is not None:
                distance = max(0.1, min(100, distance))  # 限制范围
                self.camera_state['distance'] = distance

            if azimuth is not None:
                azimuth = azimuth % 360  # 确保在0-360范围内
                self.camera_state['azimuth'] = azimuth

            if elevation is not None:
                elevation = max(-90, min(90, elevation))  # 限制范围
                self.camera_state['elevation'] = elevation

            if center_x is not None:
                center_x = max(-10.0, min(10.0, center_x))  # 限制范围
                self.camera_state['center'][0] = center_x

            if center_y is not None:
                center_y = max(-10.0, min(10.0, center_y))  # 限制范围
                self.camera_state['center'][1] = center_y

            if center_z is not None:
                center_z = max(-10.0, min(10.0, center_z))  # 限制范围
                self.camera_state['center'][2] = center_z

            # 应用相机设置
            self._apply_camera_settings()

            # 返回实际设置的值
            result = {
                'distance': self.camera_state['distance'],
                'azimuth': self.camera_state['azimuth'],
                'elevation': self.camera_state['elevation'],
                'center_x': self.camera_state['center'][0],
                'center_y': self.camera_state['center'][1],
                'center_z': self.camera_state['center'][2]
            }

            print(f"相机参数已设置: {result}")
            return result

        except Exception as e:
            print(f"设置相机参数时出错: {e}")
            return None

        finally:
            self.view_update_blocked = False

    def _apply_camera_settings(self):
        try:
            # 设置相机参数
            self.gl_view.setCameraParams(
                elevation=self.camera_state['elevation'],
                azimuth=self.camera_state['azimuth']
            )

            # 设置相机位置
            center = self.camera_state['center']
            self.gl_view.setCameraPosition(
                distance=self.camera_state['distance'],
                pos=QVector3D(center[0], center[1], center[2])
            )

        except Exception as e:
            print(f"应用相机设置时出错: {e}")

    def _apply_link_material(self, mesh_item, link_name):
        """
        为网格项应用材质颜色

        Args:
            mesh_item: OpenGL网格项
            link_name: 链接名称
        """
        try:
            # 检查是否有为此链接定义的特定材质
            if link_name in self.link_materials:
                material_info = self.link_materials[link_name]
                color = material_info.get('color', [0.7, 0.7, 0.7, 1.0])
            else:
                # 使用默认颜色
                color = [0.7, 0.7, 0.7, 1.0]  # 默认灰色

            # 设置网格颜色
            mesh_item.setColor(color)

        except Exception as e:
            print(f"设置链接 {link_name} 材质时出错: {e}")
            # 使用默认颜色作为后备
            mesh_item.setColor([0.7, 0.7, 0.7, 1.0])

    def apply_link_material(self, mesh_item, link_name):
        """为链接应用材质"""
        if link_name in self.link_materials:
            # 使用URDF中定义的颜色
            color = self.link_materials[link_name]['color']
            mesh_item.setColor(tuple(color))
        else:
            # 使用默认颜色，但根据链接类型设置不同颜色
            if 'base' in link_name.lower():
                mesh_item.setColor((0.8, 0.8, 0.9, 1.0))  # 浅蓝灰色
            elif 'thumb' in link_name.lower():
                mesh_item.setColor((0.9, 0.7, 0.7, 1.0))  # 浅红色
            elif 'index' in link_name.lower():
                mesh_item.setColor((0.7, 0.9, 0.7, 1.0))  # 浅绿色
            elif 'middle' in link_name.lower():
                mesh_item.setColor((0.7, 0.7, 0.9, 1.0))  # 浅蓝色
            elif 'ring' in link_name.lower():
                mesh_item.setColor((0.9, 0.9, 0.7, 1.0))  # 浅黄色
            elif 'pinky' in link_name.lower():
                mesh_item.setColor((0.9, 0.7, 0.9, 1.0))  # 浅紫色
            else:
                mesh_item.setColor((0.8, 0.8, 0.8, 1.0))  # 默认灰色

    def get_base_color(self, link_name):
        """获取链接的基础颜色"""
        if link_name in self.link_materials:
            return self.link_materials[link_name]['color']
        elif 'base' in link_name.lower():
            return [0.8, 0.8, 0.9, 1.0]
        elif 'thumb' in link_name.lower():
            return [0.9, 0.7, 0.7, 1.0]
        elif 'index' in link_name.lower():
            return [0.7, 0.9, 0.7, 1.0]
        elif 'middle' in link_name.lower():
            return [0.7, 0.7, 0.9, 1.0]
        elif 'ring' in link_name.lower():
            return [0.9, 0.9, 0.7, 1.0]
        elif 'pinky' in link_name.lower():
            return [0.9, 0.7, 0.9, 1.0]
        else:
            return [0.8, 0.8, 0.8, 1.0]

    def update_material_properties(self):
        """更新材质属性"""
        glossiness = 0 / 100.0
        metalness = 0 / 100.0
        transparency = 15 / 100.0

        for item in self.mesh_items:
            if hasattr(item, 'link_name'):
                link_name = item.link_name

                # 获取基础颜色
                if self.current_material_type == "Default":
                    self.apply_link_material(item, link_name)
                else:
                    # 根据材质类型调整颜色
                    base_color = self.get_base_color(link_name)
                    final_color = self.apply_material_effect(base_color, glossiness, metalness, transparency)
                    item.setColor(final_color)

                # 设置渲染选项
                if transparency > 0:
                    item.setGLOptions('translucent')
                else:
                    item.setGLOptions('opaque')

                # 材质类型特定的渲染设置已通过颜色和透明度体现

        # 使用定时器延迟更新，避免频繁刷新
        if not self.pending_update and not self.view_update_blocked:
            self.pending_update = True
            self.update_timer.start()

    def apply_material_effect(self, base_color, glossiness, metalness, transparency):
        """应用材质效果到颜色"""
        import numpy as np

        color = np.array(base_color[:3])  # RGB部分
        alpha = base_color[3] * (1.0 - transparency)  # 应用透明度

        if self.current_material_type == "Metallic":
            # 金属效果：增加反射，降低饱和度
            color = color * (1 - metalness * 0.3) + np.array([0.9, 0.9, 0.9]) * metalness * 0.3
            color = color * (1 - glossiness * 0.2) + np.array([1.0, 1.0, 1.0]) * glossiness * 0.2

        elif self.current_material_type == "Plastic":
            # 塑料效果：增加饱和度和光泽
            color = np.clip(color * (1 + glossiness * 0.3), 0, 1)

        elif self.current_material_type == "Glass":
            # 玻璃效果：增加透明度和反射
            color = color * 0.8 + np.array([0.9, 0.9, 1.0]) * 0.2
            alpha = min(alpha, 0.7)

        elif self.current_material_type == "Matte":
            # 哑光效果：降低光泽，增加漫反射
            color = color * 0.9

        elif self.current_material_type == "Glossy":
            # 光滑效果：增加反射
            color = color * (1 - glossiness * 0.1) + np.array([1.0, 1.0, 1.0]) * glossiness * 0.1

        elif self.current_material_type == "Skin":
            # 皮肤效果：温暖的色调
            color = color * 0.8 + np.array([1.0, 0.8, 0.7]) * 0.2

        elif self.current_material_type == "Robot":
            # 机器人效果：金属灰色调
            color = color * 0.7 + np.array([0.7, 0.8, 0.9]) * 0.3

        return tuple(np.clip(color, 0, 1).tolist() + [alpha])

    def _update_affected_meshes(self, affected_links):
        """
        更新受影响的网格显示

        当关节运动时，只更新受影响的链接网格，而不是重新渲染整个模型。
        这种选择性更新策略大大提高了实时交互的性能。
        使用顶点缓存进一步优化重复计算。

        Args:
            affected_links (set): 受影响的链接名称集合
        """
        if not affected_links:
            return

        try:
            # 遍历所有网格项，只更新受影响的链接
            for mesh_item in self.mesh_items:
                if hasattr(mesh_item, 'link_name') and mesh_item.link_name in affected_links:
                    link_name = mesh_item.link_name
                    if link_name in self.mesh_data:
                        # 获取原始网格数据（未变换的）
                        vertices = self.mesh_data[link_name]['vertices']
                        faces = self.mesh_data[link_name]['faces']

                        # 获取链接的当前变换矩阵
                        link_transform = self.transform_calculator.get_link_transform(link_name)

                        # 应用视觉变换（如果存在）
                        if link_name in self.visual_transforms:
                            transform = np.dot(link_transform, self.visual_transforms[link_name])
                        else:
                            transform = link_transform

                        # 创建缓存键，用于避免重复计算
                        cache_key = (link_name, id(transform))

                        # 检查缓存中是否已有变换后的顶点
                        if cache_key in self.vertex_cache:
                            vertices_transformed = self.vertex_cache[cache_key]
                        else:
                            # 计算变换后的顶点
                            vertices_homogeneous = np.hstack((vertices, np.ones((vertices.shape[0], 1))))
                            vertices_transformed = np.dot(vertices_homogeneous, transform.T)[:, :3]

                            # 限制缓存大小，防止内存过度使用
                            if len(self.vertex_cache) > 50:
                                self.vertex_cache.clear()

                            # 缓存变换结果
                            self.vertex_cache[cache_key] = vertices_transformed

                        # 更新网格显示数据
                        mesh_item.setMeshData(vertexes=vertices_transformed, faces=faces)

            # 触发异步视图更新，避免阻塞UI
            if not self.pending_update:
                self.pending_update = True
                self.update_timer.start()

        except Exception as e:
            print(f"更新网格错误: {e}")

    def finger_control(self, finger_names: list, percentages: list):
        # 确保手指名称和百分比数量匹配
        assert len(finger_names) == len(percentages)

        finger_groups = self.finger_group_analyzer.get_finger_groups()
        affected_links = set()  # 收集所有受影响的链接

        # 处理每个手指
        for finger_name, percentage in zip(finger_names, percentages):
            # 检查手指组是否存在
            if finger_name not in finger_groups['finger_groups']:
                continue

            group_config = finger_groups['finger_groups'][finger_name]

            # 获取控制范围 - 使用组内第一个关节的信息作为参考
            first_joint_name = group_config['joints'][0]
            if first_joint_name not in group_config['joint_info']:
                continue

            joint_info = group_config['joint_info'][first_joint_name]
            control_value = (joint_info["upper"] - joint_info["lower"]) * percentage / 100

            # 根据联动比例计算每个关节的目标值
            for i, joint_name in enumerate(group_config['joints']):
                if i >= len(group_config['ratios']):
                    continue

                ratio = group_config['ratios'][i]  # 获取该关节的联动比例

                if joint_name not in group_config['joint_info']:
                    continue

                joint_info = group_config['joint_info'][joint_name]

                # 应用联动比例计算实际关节角度
                joint_value = control_value * ratio

                # 限制关节值在有效范围内，防止超出物理限制
                joint_value = max(joint_info['lower'], min(joint_info['upper'], joint_value))

                # 更新关节值并获取受影响的链接
                try:
                    links = self.transform_calculator.update_joint_value(joint_name, joint_value)
                    affected_links.update(links)
                    self.joint_values[joint_name] = joint_value
                except Exception as e:
                    print(f"更新关节 {joint_name} 时出错: {e}")

        # 批量更新受影响的网格，提高渲染效率
        if affected_links:
            try:
                self._update_affected_meshes(affected_links)
            except Exception as e:
                print(f"更新网格时出错: {e}")

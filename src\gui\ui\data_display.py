# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'data_display.ui'
##
## Created by: Qt User Interface Compiler version 6.4.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, Q<PERSON>olor, Q<PERSON><PERSON>al<PERSON>rad<PERSON>, Q<PERSON>ursor,
    <PERSON><PERSON><PERSON>, QFontDatabase, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QHBoxLayout, QPushButton, QSizePolicy,
    QSpacerItem, QTabWidget, QVBoxLayout, QWidget)

from ..components.radiobutton import CustomRadioButton

class Ui_Form(object):
    def setupUi(self, Form):
        if not Form.objectName():
            Form.setObjectName(u"Form")
        Form.resize(863, 372)
        sizePolicy = QSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(Form.sizePolicy().hasHeightForWidth())
        Form.setSizePolicy(sizePolicy)
        self.verticalLayout = QVBoxLayout(Form)
        self.verticalLayout.setSpacing(7)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setSpacing(16)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.pushButton_chart = QPushButton(Form)
        self.pushButton_chart.setObjectName(u"pushButton_chart")

        self.horizontalLayout.addWidget(self.pushButton_chart)

        self.pushButton_number = QPushButton(Form)
        self.pushButton_number.setObjectName(u"pushButton_number")

        self.horizontalLayout.addWidget(self.pushButton_number)

        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer_3)

        self.radioButton_number = CustomRadioButton(Form)
        self.radioButton_number.setObjectName(u"radioButton_number")

        self.horizontalLayout.addWidget(self.radioButton_number)

        self.radioButton_percentage = CustomRadioButton(Form)
        self.radioButton_percentage.setObjectName(u"radioButton_percentage")

        self.horizontalLayout.addWidget(self.radioButton_percentage)


        self.verticalLayout.addLayout(self.horizontalLayout)

        self.horizontalLayout_2 = QHBoxLayout()
        self.horizontalLayout_2.setSpacing(16)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Minimum, QSizePolicy.Fixed)

        self.horizontalLayout_2.addItem(self.horizontalSpacer_2)

        self.pushButton_position = QPushButton(Form)
        self.pushButton_position.setObjectName(u"pushButton_position")

        self.horizontalLayout_2.addWidget(self.pushButton_position)

        self.pushButton_speed = QPushButton(Form)
        self.pushButton_speed.setObjectName(u"pushButton_speed")

        self.horizontalLayout_2.addWidget(self.pushButton_speed)

        self.pushButton_force = QPushButton(Form)
        self.pushButton_force.setObjectName(u"pushButton_force")

        self.horizontalLayout_2.addWidget(self.pushButton_force)

        self.horizontalSpacer_4 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer_4)


        self.verticalLayout.addLayout(self.horizontalLayout_2)

        self.tabWidget = QTabWidget(Form)
        self.tabWidget.setObjectName(u"tabWidget")

        self.verticalLayout.addWidget(self.tabWidget)

        self.verticalLayout.setStretch(2, 1)

        self.retranslateUi(Form)

        QMetaObject.connectSlotsByName(Form)
    # setupUi

    def retranslateUi(self, Form):
        Form.setWindowTitle(QCoreApplication.translate("Form", u"Form", None))
        self.pushButton_chart.setText(QCoreApplication.translate("Form", u"\u56fe\u8868\u5c55\u793a", None))
        self.pushButton_number.setText(QCoreApplication.translate("Form", u"\u6570\u5b57\u663e\u793a", None))
        self.radioButton_number.setText(QCoreApplication.translate("Form", u"Number", None))
        self.radioButton_percentage.setText(QCoreApplication.translate("Form", u"Percentage", None))
        self.pushButton_position.setText(QCoreApplication.translate("Form", u"Position", None))
        self.pushButton_speed.setText(QCoreApplication.translate("Form", u"Speed", None))
        self.pushButton_force.setText(QCoreApplication.translate("Form", u"Force", None))
    # retranslateUi


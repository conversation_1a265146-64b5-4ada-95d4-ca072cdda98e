#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试URDF查看器集成到GestureEditDialog的功能
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PySide6.QtCore import Qt

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.gui.widgets.gesture_edit_dialog import GestureEditDialog
from src.gui.widgets.gl_widget.urdf_viewer import URDFViewer
from src.core.gesture_data_manager import GestureData, StepData
from src import APP_ROOT_PATH


class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试URDF查看器集成")
        self.setGeometry(100, 100, 400, 300)
        
        # 创建共享的URDF查看器
        self.shared_urdf_viewer = URDFViewer()
        urdf_path = os.path.join(APP_ROOT_PATH, "brainco-righthand-URDF-V2", "urdf", "brainco-righthand-URDF-V2.urdf")
        if os.path.exists(urdf_path):
            self.shared_urdf_viewer.load_urdf(urdf_path)
        else:
            print(f"URDF文件不存在: {urdf_path}")
        
        # 创建测试按钮
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        test_button = QPushButton("打开手势编辑对话框（带URDF查看器）")
        test_button.clicked.connect(self.open_gesture_dialog)
        layout.addWidget(test_button)
        
        test_button2 = QPushButton("打开手势编辑对话框（不带URDF查看器）")
        test_button2.clicked.connect(self.open_gesture_dialog_without_urdf)
        layout.addWidget(test_button2)
    
    def open_gesture_dialog(self):
        """打开带URDF查看器的手势编辑对话框"""
        # 创建测试用的手势数据
        gesture_data = GestureData(1, "测试手势", True)
        step_data = StepData(0, 1200, 2, [0] * 6, [100] * 6, [0] * 6)
        gesture_data.steps = [step_data]
        
        all_gesture_names = ["测试手势", "其他手势"]
        
        dialog = GestureEditDialog(
            "测试手势", 
            gesture_data, 
            all_gesture_names, 
            parent=self, 
            shared_urdf_viewer=self.shared_urdf_viewer
        )
        dialog.load_gesture_data()
        dialog.exec()
    
    def open_gesture_dialog_without_urdf(self):
        """打开不带URDF查看器的手势编辑对话框（使用静态图片）"""
        # 创建测试用的手势数据
        gesture_data = GestureData(2, "测试手势2", True)
        step_data = StepData(0, 1200, 2, [0] * 6, [100] * 6, [0] * 6)
        gesture_data.steps = [step_data]
        
        all_gesture_names = ["测试手势2", "其他手势"]
        
        dialog = GestureEditDialog(
            "测试手势2", 
            gesture_data, 
            all_gesture_names, 
            parent=self, 
            shared_urdf_viewer=None  # 不传入URDF查看器
        )
        dialog.load_gesture_data()
        dialog.exec()


def main():
    app = QApplication(sys.argv)
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()

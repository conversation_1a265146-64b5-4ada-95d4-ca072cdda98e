#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import json
import time
import requests
from pathlib import Path
from PySide6.QtWidgets import (QApplication, QMainWindow, QLabel, QPushButton,
                               QVBoxLayout, QHBoxLayout, QWidget, QProgressBar,
                               QMessageBox, QTextEdit, QFileDialog, QDialog)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont, QIcon, QPixmap

# 模拟当前固件版本
CURRENT_VERSION = "1.0.0"

# 模拟服务器API地址
API_URL = "https://api.example.com/firmware/latest"

def setup_application_icon(app):
    """设置应用程序图标 - Windows 11 优化版本"""
    icon_set = False

    # 尝试多个可能的图标路径
    current_dir = os.path.dirname(__file__)
    icon_paths = [
        # 相对路径
        "../resources/icons/revo2_hand.ico",  # 优先使用ico格式
        "../resources/icons/brain-cog.png",
        "../resources/icons/brain-cog.ico",
        "../../assets/icons/app_icon.ico",
        "../../assets/icons/app_icon.png",
        # 绝对路径
        os.path.join(current_dir, "../resources/icons/revo2_hand.ico"),
        os.path.join(current_dir, "../resources/icons/brain-cog.png"),
        os.path.join(current_dir, "../resources/icons/brain-cog.ico"),
        os.path.join(current_dir, "../../assets/icons/app_icon.ico"),
        os.path.join(current_dir, "../../assets/icons/app_icon.png"),
        # 测试图标
        "test_icon.ico",
    ]

    for icon_path in icon_paths:
        if os.path.exists(icon_path):
            try:
                icon = QIcon(icon_path)
                if not icon.isNull():
                    # Windows 11 特殊处理
                    app.setWindowIcon(icon)

                    # 设置应用程序ID (Windows 11任务栏分组)
                    try:
                        import ctypes
                        myappid = 'brainco.revo.firmware.updater.1.0'
                        ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)
                    except:
                        pass

                    icon_set = True
                    print(f"已设置应用图标: {icon_path}")
                    break
            except Exception as e:
                print(f"加载图标失败 {icon_path}: {e}")
                continue

    if not icon_set:
        # 使用系统默认图标
        try:
            app.setWindowIcon(app.style().standardIcon(app.style().SP_ComputerIcon))
            print("使用系统默认图标")
        except:
            print("无法设置图标")

    return icon_set

class DownloadThread(QThread):
    progress_updated = Signal(int)
    download_complete = Signal(str)
    download_error = Signal(str)
    
    def __init__(self, url, save_path):
        super().__init__()
        self.url = url
        self.save_path = save_path
        
    def run(self):
        try:
            # 模拟下载过程
            for i in range(101):
                time.sleep(0.05)  # 模拟下载延迟
                self.progress_updated.emit(i)
            
            # 模拟创建固件文件
            with open(self.save_path, 'w') as f:
                f.write("This is a simulated firmware file v2.0.0")
                
            self.download_complete.emit(self.save_path)
        except Exception as e:
            self.download_error.emit(str(e))

class UpgradeThread(QThread):
    progress_updated = Signal(int)
    upgrade_complete = Signal()
    upgrade_error = Signal(str)
    
    def __init__(self, firmware_path):
        super().__init__()
        self.firmware_path = firmware_path
        
    def run(self):
        try:
            # 模拟升级过程
            for i in range(101):
                time.sleep(0.1)  # 模拟升级延迟
                self.progress_updated.emit(i)
            
            self.upgrade_complete.emit()
        except Exception as e:
            self.upgrade_error.emit(str(e))


class OTADialog(QDialog):
    def __init__(self, app_icon=None):
        super().__init__()

        self.setWindowTitle("BrainCo Revo 固件更新工具")
        self.setMinimumSize(500, 400)

        # 设置窗口图标
        if app_icon and not app_icon.isNull():
            self.setWindowIcon(app_icon)

        self.init_ui()
        
    def init_ui(self):
        # 主布局
        main_layout = QVBoxLayout()
        
        # 当前版本信息
        version_layout = QHBoxLayout()
        version_layout.addWidget(QLabel("当前固件版本:"))
        self.current_version_label = QLabel(CURRENT_VERSION)
        self.current_version_label.setFont(QFont("Arial", 10, QFont.Bold))
        version_layout.addWidget(self.current_version_label)
        version_layout.addStretch()
        main_layout.addLayout(version_layout)
        
        # 分隔线
        line = QLabel()
        line.setFrameShape(QLabel.HLine)
        line.setFrameShadow(QLabel.Sunken)
        main_layout.addWidget(line)
        
        # 更新信息区域
        self.update_info_area = QWidget()
        self.update_info_layout = QVBoxLayout(self.update_info_area)
        
        # 新版本标签
        self.new_version_label = QLabel("检测到新版本: v2.0.0")
        self.new_version_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.new_version_label.setStyleSheet("color: #0066cc;")
        self.update_info_layout.addWidget(self.new_version_label)
        
        # 更新内容
        self.update_info_layout.addWidget(QLabel("更新内容:"))
        self.changelog = QTextEdit()
        self.changelog.setReadOnly(True)
        self.changelog.setText("1. 修复了多个稳定性问题\n2. 优化了电机控制算法\n3. 增加了新的手势识别功能\n4. 提高了电池续航能力")
        self.update_info_layout.addWidget(self.changelog)
        
        # 下载和升级按钮
        button_layout = QHBoxLayout()
        self.download_btn = QPushButton("下载更新")
        self.download_btn.clicked.connect(self.download_firmware)
        button_layout.addWidget(self.download_btn)
        
        self.upgrade_btn = QPushButton("安装更新")
        self.upgrade_btn.setEnabled(False)
        self.upgrade_btn.clicked.connect(self.upgrade_firmware)
        button_layout.addWidget(self.upgrade_btn)
        
        self.update_info_layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.update_info_layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("")
        self.update_info_layout.addWidget(self.status_label)
        
        main_layout.addWidget(self.update_info_area)
        self.update_info_area.setVisible(False)
        
        # 检查更新按钮
        check_update_btn = QPushButton("检查更新")
        check_update_btn.clicked.connect(self.check_for_updates)
        main_layout.addWidget(check_update_btn)

        self.setLayout(main_layout)
        
        # 保存固件路径
        self.firmware_path = None
        
    def check_for_updates(self):
        self.status_label.setText("正在检查更新...")
        
        # 模拟网络请求延迟
        QTimer.singleShot(1500, self.process_update_check)
        
    def process_update_check(self):
        # 模拟检查更新的结果
        # 实际应用中，这里应该从API获取最新版本信息
        new_version = "2.0.0"
        
        if self.version_compare(new_version, CURRENT_VERSION) > 0:
            self.update_info_area.setVisible(True)
            self.status_label.setText("发现新版本！")
        else:
            self.update_info_area.setVisible(False)
            QMessageBox.information(self, "更新检查", "您的固件已是最新版本。")
            self.status_label.setText("固件已是最新版本")
            
    def version_compare(self, version1, version2):
        """比较两个版本号"""
        v1_parts = [int(x) for x in version1.split('.')]
        v2_parts = [int(x) for x in version2.split('.')]
        
        for i in range(max(len(v1_parts), len(v2_parts))):
            v1 = v1_parts[i] if i < len(v1_parts) else 0
            v2 = v2_parts[i] if i < len(v2_parts) else 0
            
            if v1 > v2:
                return 1
            elif v1 < v2:
                return -1
                
        return 0
        
    def download_firmware(self):
        # 选择保存位置
        save_path, _ = QFileDialog.getSaveFileName(
            self, 
            "保存固件文件", 
            os.path.join(os.path.expanduser("~"), "firmware_v2.0.0.bin"),
            "固件文件 (*.bin)"
        )
        
        if not save_path:
            return
            
        self.firmware_path = save_path
        self.download_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.status_label.setText("正在下载固件...")
        
        # 模拟固件下载URL
        download_url = "https://example.com/firmware/v2.0.0.bin"
        
        # 启动下载线程
        self.download_thread = DownloadThread(download_url, save_path)
        self.download_thread.progress_updated.connect(self.update_progress)
        self.download_thread.download_complete.connect(self.download_finished)
        self.download_thread.download_error.connect(self.download_failed)
        self.download_thread.start()
        
    def download_finished(self, path):
        self.status_label.setText(f"固件下载完成: {path}")
        self.upgrade_btn.setEnabled(True)
        self.download_btn.setEnabled(True)
        
    def download_failed(self, error):
        self.status_label.setText(f"下载失败: {error}")
        self.progress_bar.setVisible(False)
        self.download_btn.setEnabled(True)
        
    def upgrade_firmware(self):
        if not self.firmware_path or not os.path.exists(self.firmware_path):
            QMessageBox.warning(self, "错误", "固件文件不存在，请重新下载")
            return
            
        reply = QMessageBox.question(
            self, 
            "确认升级", 
            "准备开始固件升级，设备将在升级过程中无法使用。是否继续？",
            QMessageBox.Yes | QMessageBox.No, 
            QMessageBox.No
        )
        
        if reply == QMessageBox.No:
            return
            
        self.download_btn.setEnabled(False)
        self.upgrade_btn.setEnabled(False)
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(True)
        self.status_label.setText("正在升级固件...")
        
        # 启动升级线程
        self.upgrade_thread = UpgradeThread(self.firmware_path)
        self.upgrade_thread.progress_updated.connect(self.update_progress)
        self.upgrade_thread.upgrade_complete.connect(self.upgrade_finished)
        self.upgrade_thread.upgrade_error.connect(self.upgrade_failed)
        self.upgrade_thread.start()
        
    def upgrade_finished(self):
        self.status_label.setText("固件升级成功！")
        self.progress_bar.setVisible(False)
        
        # 更新当前版本显示
        self.current_version_label.setText("2.0.0")
        
        # 隐藏更新区域
        QTimer.singleShot(2000, lambda: self.update_info_area.setVisible(False))
        
        QMessageBox.information(self, "升级完成", "固件已成功升级到最新版本！")
        
    def upgrade_failed(self, error):
        self.status_label.setText(f"升级失败: {error}")
        self.progress_bar.setVisible(False)
        self.download_btn.setEnabled(True)
        self.upgrade_btn.setEnabled(True)
        
    def update_progress(self, value):
        self.progress_bar.setValue(value)

def set_windows_taskbar_icon():
    """Windows 11 任务栏图标特殊处理"""
    try:
        import ctypes
        from ctypes import wintypes

        # 设置应用程序用户模型ID (Windows 7+)
        myappid = 'brainco.revo.firmware.updater.1.0'
        ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)

        # Windows 11 特殊处理 - 强制刷新任务栏
        try:
            # 获取当前进程句柄
            kernel32 = ctypes.windll.kernel32
            user32 = ctypes.windll.user32

            # 刷新任务栏
            user32.UpdateWindow(user32.GetDesktopWindow())

        except Exception as e:
            print(f"Windows 11 任务栏刷新失败: {e}")

    except ImportError:
        print("无法导入Windows API，跳过任务栏特殊处理")
    except Exception as e:
        print(f"设置Windows任务栏图标失败: {e}")

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Windows 11 任务栏图标处理
    set_windows_taskbar_icon()

    # 设置应用程序名称和版本（在某些系统上会影响任务栏显示）
    app.setApplicationName("BrainCo Revo 固件更新工具")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("BrainCo")
    app.setOrganizationDomain("brainco.tech")

    # 设置应用程序图标（任务栏图标）
    icon_set = setup_application_icon(app)

    # 获取当前设置的图标，传递给窗口
    app_icon = app.windowIcon()

    window = OTADialog(app_icon)

    # 确保窗口图标设置
    if icon_set and not app_icon.isNull():
        window.setWindowIcon(app_icon)

    window.show()

    # Windows 11 - 窗口显示后再次设置图标
    if os.name == 'nt':  # Windows系统
        QTimer.singleShot(100, lambda: window.setWindowIcon(app_icon))

    print("固件更新工具已启动")
    print(f"图标设置状态: {'成功' if icon_set else '使用默认图标'}")

    sys.exit(app.exec())

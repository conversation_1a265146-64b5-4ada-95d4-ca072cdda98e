import time

from PySide6.QtWidgets import (<PERSON><PERSON><PERSON><PERSON>, Q<PERSON>rogressBar, QVBoxLayout, QLabel,
                               QHBoxLayout, QSizePolicy, QFrame, QSpacerItem)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont


class OTADownloadDialog(QDialog):

    def __init__(self, parent=None):
        super(OTADownloadDialog, self).__init__(parent)
        self.setWindowTitle('OTA')
        self.is_ok = False
        self.setWindowFlag(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        self.setFixedSize(480, 320)
        self._init_ui()
        self._apply_styles()

        self._start_time = None
        self._update_timer = QTimer()
        self._update_timer.timeout.connect(self._update_duration)

    def _init_ui(self):
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)
        self.setLayout(main_layout)

        self.label_info = QLabel("")
        self.label_info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label_info.setObjectName("infoLabel")
        self.label_info.setWordWrap(True)

        # 中间更新信息区域
        info_frame = QFrame()
        info_frame.setObjectName("infoFrame")
        info_layout = QVBoxLayout(info_frame)
        info_layout.setContentsMargins(20, 15, 20, 15)
        info_layout.setSpacing(8)

        self.status_label = QLabel()
        self.status_label.setText(self.tr("State: IDLE"))
        self.status_label.setObjectName("statusLabel")

        info_layout.addWidget(self.status_label)

        # 底部进度条和时间区域
        progress_frame = QFrame()
        progress_frame.setObjectName("progressFrame")
        progress_layout = QVBoxLayout(progress_frame)
        progress_layout.setContentsMargins(0, 15, 0, 0)
        progress_layout.setSpacing(12)

        # 进度条
        self.progressBar = QProgressBar()
        self.progressBar.setObjectName("progressBar")
        self.progressBar.setMinimum(0)
        self.progressBar.setMaximum(100)
        self.progressBar.setValue(0)
        self.progressBar.setTextVisible(True)
        self.progressBar.setFormat("%p%")

        # 时间信息布局
        time_layout = QHBoxLayout()
        time_layout.setContentsMargins(0, 0, 0, 0)

        self.duration_label = QLabel("Elapsed: 0s")

        self.duration_label.setObjectName("timeLabel")

        self.remaining_label = QLabel()
        self.remaining_label.setText(self.tr("Remaining: ---"))

        self.remaining_label.setObjectName("timeLabel")

        time_layout.addWidget(self.duration_label)
        time_layout.addStretch()
        time_layout.addWidget(self.remaining_label)

        progress_layout.addWidget(self.progressBar)
        progress_layout.addLayout(time_layout)

        # 添加到主布局
        main_layout.addWidget(self.label_info)
        main_layout.addSpacerItem(QSpacerItem(20, 10, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))
        main_layout.addWidget(info_frame)
        main_layout.addSpacerItem(QSpacerItem(20, 10, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))
        main_layout.addWidget(progress_frame)

    def _apply_styles(self):
        self.setStyleSheet("""
            QDialog {
                background-color: #2A2D32;
                border-radius: 12px;
            }

            #titleLabel {
                font-family: 'Alibaba PuHuiTi 2.0', 'Microsoft YaHei';
                font-size: 20px;
                font-weight: bold;
                color: #FFFFFF;
                padding: 10px 0;
            }

            #infoLabel {
                font-family: 'Alibaba PuHuiTi 2.0', 'Microsoft YaHei';
                font-size: 14px;
                color: #B0B0B0;
                padding: 5px 0;
            }

            #infoFrame {
                background-color: #35383E;
                border-radius: 8px;
                border: 1px solid #404449;
            }

            #statusLabel, #speedLabel {
                font-family: 'Alibaba PuHuiTi 2.0', 'Microsoft YaHei';
                font-size: 13px;
                color: #E0E0E0;
            }

            #progressFrame {
                background-color: transparent;
            }

            #progressBar {
                border: 2px solid #404449;
                border-radius: 8px;
                text-align: center;
                background-color: #35383E;
                color: #FFFFFF;
                font-family: 'Alibaba PuHuiTi 2.0', 'Microsoft YaHei';
                font-size: 12px;
                font-weight: bold;
                height: 24px;
            }

            #progressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4CAF50, stop:0.5 #66BB6A, stop:1 #4CAF50);
                border-radius: 6px;
                margin: 1px;
            }

            #timeLabel {
                font-family: 'Alibaba PuHuiTi 2.0', 'Microsoft YaHei';
                font-size: 12px;
                color: #A0A0A0;
            }
        """)

    def _update_duration(self):
        if self._start_time is not None:
            duration = int(time.time() - self._start_time)
            self.duration_label.setText(f"Elapsed: {self._format_time(duration)}")

            # 计算预计剩余时间
            current_progress = self.progressBar.value()
            if current_progress > 0:
                total_estimated = duration * 100 / current_progress
                remaining = max(0, int(total_estimated - duration))
                self.remaining_label.setText(self.tr("Remaining: ") + f"{self._format_time(remaining)}")

    def _format_time(self, seconds):
        if seconds < 60:
            return f"{seconds}s"
        elif seconds < 3600:
            minutes = seconds // 60
            secs = seconds % 60
            return f"{minutes}min{secs}s"
        else:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            return f"{hours}h{minutes}min"

    def update_state(self, state: str):
        self.status_label.setText(self.tr("State: ") + state)

    def update_progress(self, progress):
        if isinstance(progress, float):
            progress = int(progress)

        self.progressBar.setValue(progress)

        if self._start_time is None:
            self._start_time = time.time()
            self._update_timer.start(1000)  # 每秒更新一次时间显示

        self._update_duration()

    def update_info(self, info_text):
        self.label_info.setText(info_text)

    def close_dialog(self):
        self.is_ok = True
        self._update_timer.stop()
        self.close()

    def show_dialog(self, text):
        self.is_ok = False
        self._start_time = None
        self._update_timer.stop()

        # 重置所有显示
        self.label_info.setText(text)
        self.status_label.setText("")
        self.duration_label.setText(self.tr("Elapsed: 0s"))
        self.remaining_label.setText(self.tr("Remaining: ---"))
        self.progressBar.setValue(0)

        self.exec()

    def closeEvent(self, event):
        if self.is_ok:
            self._update_timer.stop()
            event.accept()
        else:
            event.ignore()

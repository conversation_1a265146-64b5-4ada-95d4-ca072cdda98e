# -*- coding: utf-8 -*-
"""
URDF解析模块

提供URDF文件解析、路径转换和关节分析功能
"""

import os
import xml.etree.ElementTree as ET
from typing import Dict, List, Tuple, Optional


class URDFPathConverter:
    """URDF路径转换器，处理ROS package路径到实际文件路径的转换"""

    def __init__(self, workspace_root: str):
        """
        初始化URDF路径转换器

        Args:
            workspace_root (str): 工作空间根目录路径
        """
        self.workspace_root = workspace_root

    def convert_package_path(self, package_path: str) -> str:
        """
        将ROS package路径转换为实际文件路径

        ROS中的package://路径是一种特殊的URI格式，需要转换为实际的文件系统路径

        Args:
            package_path (str): ROS package路径，格式如 package://package_name/path/to/file

        Returns:
            str: 转换后的实际文件路径

        Example:
            package://Brain-righthand-URDF-V2/meshes/base_link.STL
            -> workspace_root/Brain-righthand-URDF-V2/meshes/base_link.STL
        """
        if package_path.startswith("package://"):
            # 移除 package:// 前缀（10个字符）
            relative_path = package_path[10:]
            # 构建完整路径
            full_path = os.path.join(self.workspace_root, relative_path)
            return full_path.replace('\\', '/')  # 统一使用正斜杠，避免Windows路径问题
        return package_path

    def convert_urdf_file(self, input_urdf_path: str, output_urdf_path: str) -> str:
        """
        转换URDF文件中的所有package路径为实际路径

        遍历URDF文件中的所有mesh元素，将package://路径转换为实际文件路径
        这样可以确保URDF解析器能够正确找到网格文件

        Args:
            input_urdf_path (str): 输入URDF文件路径
            output_urdf_path (str): 输出URDF文件路径

        Returns:
            str: 转换后的URDF文件路径，如果转换失败则返回原路径
        """
        try:
            # 解析XML文档
            tree = ET.parse(input_urdf_path)
            root = tree.getroot()

            # 查找所有mesh元素并转换路径
            for mesh in root.iter('mesh'):
                filename = mesh.get('filename')
                if filename and filename.startswith('package://'):
                    new_path = self.convert_package_path(filename)
                    mesh.set('filename', new_path)
                    print(f"转换路径: {filename} -> {new_path}")

            # 保存转换后的文件
            tree.write(output_urdf_path, encoding='utf-8', xml_declaration=True)
            print(f"转换后的URDF文件保存到: {output_urdf_path}")
            return output_urdf_path

        except Exception as e:
            print(f"转换URDF文件时出错: {e}")
            return input_urdf_path


class URDFJointAnalyzer:
    """URDF关节分析器，提取可控关节信息"""

    def __init__(self, urdf_path: str):
        """
        初始化URDF关节分析器

        Args:
            urdf_path (str): URDF文件路径
        """
        self.urdf_path = urdf_path
        self.joints_info = {}  # 存储可控关节信息的字典
        self._analyze_joints()  # 立即分析关节信息

    def _analyze_joints(self):
        """
        分析URDF文件中的关节信息

        解析URDF文件，提取所有非固定关节的信息，包括：
        - 关节类型（revolute, prismatic等）
        - 运动范围限制
        - 最大力矩和速度

        只保留有运动能力的关节（effort > 0），过滤掉固定关节和无效关节
        """
        try:
            # 解析URDF XML文件
            tree = ET.parse(self.urdf_path)
            root = tree.getroot()

            # 遍历所有关节元素
            for joint in root.iter('joint'):
                joint_name = joint.get('name')
                joint_type = joint.get('type')

                # 跳过固定关节，因为它们不能运动
                if joint_type == 'fixed':
                    continue

                # 获取关节运动限制信息
                limit_elem = joint.find('limit')
                if limit_elem is not None:
                    # 解析关节限制参数
                    lower = float(limit_elem.get('lower', '0'))      # 下限角度/位置
                    upper = float(limit_elem.get('upper', '0'))      # 上限角度/位置
                    effort = float(limit_elem.get('effort', '0'))    # 最大力矩/力
                    velocity = float(limit_elem.get('velocity', '1')) # 最大速度

                    # 只包含有运动能力的关节（effort > 0表示可以施加力矩）
                    if effort > 0:
                        self.joints_info[joint_name] = {
                            'type': joint_type,
                            'lower': lower,
                            'upper': upper,
                            'effort': effort,
                            'velocity': velocity
                        }

        except Exception as e:
            print(f"分析关节信息时出错: {e}")

    def get_controllable_joints(self) -> Dict:
        """
        获取可控关节信息

        Returns:
            Dict: 包含所有可控关节信息的字典
                 键为关节名称，值为关节参数字典
        """
        return self.joints_info

    def print_joint_info(self):
        """
        打印关节信息到控制台

        以格式化的方式显示所有可控关节的详细信息，
        包括类型、运动范围、最大力矩和速度等参数
        """
        print("\n=== 可控关节信息 ===")
        for name, info in self.joints_info.items():
            print(f"关节: {name}")
            print(f"  类型: {info['type']}")
            print(f"  范围: [{info['lower']:.3f}, {info['upper']:.3f}]")
            print(f"  最大力矩: {info['effort']}")
            print(f"  最大速度: {info['velocity']}")
            print()


class FingerGroupAnalyzer:
    """手指关节分组分析器"""

    def __init__(self, joint_analyzer: URDFJointAnalyzer):
        """
        初始化手指关节分组分析器

        Args:
            joint_analyzer (URDFJointAnalyzer): 关节分析器实例
        """
        self.hand_type = "left" if "left" in joint_analyzer.urdf_path else "right"
        self.joint_analyzer = joint_analyzer
        self.finger_groups = self._analyze_finger_groups()

    def _analyze_finger_groups(self) -> Dict[str, Dict]:
        """
        分析并创建手指关节分组

        根据预定义的手指关节模式，将相关的关节组合成逻辑分组，
        实现手指的联动控制。每个分组包含多个关节和对应的联动比例。

        Returns:
            Dict[str, Dict]: 包含手指分组和独立关节的字典
                - finger_groups: 手指联动分组
                - individual_joints: 不属于任何分组的独立关节
        """
        joint_info = self.joint_analyzer.get_controllable_joints()

        # 定义手指关节分组模式
        # 每个分组定义了关节名称、联动比例和功能描述
        # 支持多种命名模式（原始和right_前缀）
        finger_patterns = {
            'thumbaux': {
                'name': '拇指转动',
                'joints': [f'{self.hand_type}_thumb_metacarpal_joint'],
                'ratios': [1.0],  # 独立控制拇指根部转动
                'description': '控制拇指左右转动（对掌运动）'
            },
            'thumb': {
                'name': '拇指弯曲',
                'joints': [f'{self.hand_type}_thumb_proximal_joint',
                           f'{self.hand_type}_thumb_distal_joint'],
                'ratios': [1.0, 0.8],  # 拇指弯曲联动，远端关节运动幅度较小
                'description': '控制拇指弯曲捏合'
            },
            'index': {
                'name': '食指',
                'joints': [f'{self.hand_type}_index_proximal_joint',
                           f'{self.hand_type}_index_distal_joint',
                           f'{self.hand_type}_index_tip_joint'],
                'ratios': [1.0, 0.8, 0.6],  # 递减的联动比例，模拟自然弯曲
                'description': '控制食指弯曲'
            },
            'middle': {
                'name': '中指',
                'joints': [f'{self.hand_type}_middle_proximal_joint',
                           f'{self.hand_type}_middle_distal_joint',
                           f'{self.hand_type}_middle_tip_joint'],
                'ratios': [1.0, 0.8, 0.6],
                'description': '控制中指弯曲'
            },
            'ring': {
                'name': '无名指',
                'joints': [f'{self.hand_type}_ring_proximal_joint',
                           f'{self.hand_type}_ring_distal_joint',
                           f'{self.hand_type}_ring_tip_joint'],
                'ratios': [1.0, 0.8, 0.6],
                'description': '控制无名指弯曲'
            },
            'pinky': {
                'name': '小指',
                'joints': [f'{self.hand_type}_pinky_proximal_joint',
                           f'{self.hand_type}_pinky_distal_joint',
                           f'{self.hand_type}_pinky_tip_joint'],
                'ratios': [1.0, 0.8, 0.6],
                'description': '控制小指弯曲'
            }
        }

        # 检查哪些关节实际存在，并创建有效的分组
        # 只有在URDF中实际存在的关节才会被包含在分组中
        valid_groups = {}
        individual_joints = {}

        for group_key, group_config in finger_patterns.items():
            existing_joints = []
            existing_ratios = []

            # 检查分组中的每个关节是否在URDF中存在
            for i, joint_name in enumerate(group_config['joints']):
                if joint_name in joint_info:
                    existing_joints.append(joint_name)
                    existing_ratios.append(group_config['ratios'][i])

            # 如果至少有一个关节存在，就创建这个分组
            if len(existing_joints) >= 1:
                valid_groups[group_key] = {
                    'name': group_config['name'],
                    'joints': existing_joints,
                    'ratios': existing_ratios,
                    'joint_info': {joint: joint_info[joint] for joint in existing_joints},
                    'description': group_config.get('description', '')
                }

        # 收集所有已分组的关节
        all_finger_joints = set()
        for group in valid_groups.values():
            all_finger_joints.update(group['joints'])

        # 将不属于任何手指分组的关节归类为独立关节
        for joint_name, info in joint_info.items():
            if joint_name not in all_finger_joints and joint_name not in individual_joints:
                individual_joints[joint_name] = {
                    'name': joint_name,
                    'info': info
                }

        return {
            'finger_groups': valid_groups,      # 手指联动分组
            'individual_joints': individual_joints  # 独立控制关节
        }

    def get_finger_groups(self):
        """
        获取手指分组信息

        Returns:
            Dict: 包含finger_groups和individual_joints的字典
        """
        return self.finger_groups

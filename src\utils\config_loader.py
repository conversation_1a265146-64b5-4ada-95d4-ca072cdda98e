import os
from ruamel.yaml import YAML
from typing import List, Any, Optional

yaml = YAML()
yaml.default_flow_style = True

# 全部配置容器
_config = {
    "All": None,
    "Hands": None,
    "Gestures": None,
}


def _load_all_sections(config_dict):
    _config["All"] = config_dict
    _config["Hands"] = config_dict.get("Hands")
    _config["Gestures"] = config_dict.get("Gestures")


def register_parameters_to_global(settings_file):
    with open(settings_file, 'r', encoding='utf-8') as f:
        config = yaml.load(f)

    if not isinstance(config, dict):
        raise TypeError(f"Invalid YAML structure in file: {settings_file}")

    _config["SETTINGS_FILE_PATH"] = settings_file
    _load_all_sections(config)


def reload_config(new_config):
    if not isinstance(new_config, dict):
        raise TypeError("Expected a dict for config")
    _load_all_sections(new_config)

def save_config(config):
    _load_all_sections(config)
    with open(get_config("SETTINGS_FILE_PATH"), 'w', encoding='utf-8') as f:
        yaml.dump(config, f)


def get_config(base: str, sub_keys: Optional[List[str]] = None) -> Any:
    """
    获取配置项，支持父路径为点分隔字符串，子路径为列表形式：

    示例：
    - get_config("BASIC") → 返回整个 BASIC
    - get_config("BASIC.model_config", ["version"])
    - get_config("TEST_CONFIG.nested", ["subkey1", "subkey2"])

    :param base: 可以是 'BASIC' 或 'BASIC.subkey1.subkey2' 形式的路径
    :param sub_keys: 子路径，列表形式。默认 None 表示只访问 base
    :param default: 找不到时返回的默认值
    """
    current = _config

    base_keys = base.split(".")
    if len(base_keys) > 1:
        # 处理 base 路径
        for key in base_keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                raise KeyError(f"Key path not found: {' -> '.join(base_keys)}")
    elif len(base_keys) == 1:
        current = current[base_keys[0]]
    else:
        raise KeyError("Invalid base path")

    # 如果没有子路径，直接返回 base 节点结果
    if not sub_keys:
        return current

    # 继续处理子路径
    for key in sub_keys:
        if isinstance(current, dict) and key in current:
            current = current[key]
        else:
            raise KeyError(f"Key path not found: {base} -> {' -> '.join(sub_keys)}")

    return current



